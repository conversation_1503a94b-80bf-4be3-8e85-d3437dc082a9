{"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile", "reactNativePath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native", "dependencies": {"@react-native-picker/picker": {"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\@react-native-picker\\picker", "name": "@react-native-picker/picker", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\@react-native-picker\\picker\\android", "packageImportPath": "import com.reactnativecommunity.picker.RNCPickerPackage;", "packageInstance": "new RNCPickerPackage()", "buildTypes": [], "libraryName": "rnpicker", "componentDescriptors": ["RNCAndroidDialogPickerComponentDescriptor", "RNCAndroidDropdownPickerComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Desktop/jadhagam/TamilJadhagamMobile/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Desktop/jadhagam/TamilJadhagamMobile/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Desktop/jadhagam/TamilJadhagamMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Desktop/jadhagam/TamilJadhagamMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Desktop/jadhagam/TamilJadhagamMobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Desktop/jadhagam/TamilJadhagamMobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Desktop/jadhagam/TamilJadhagamMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\node_modules\\react-native-edge-to-edge\\android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "C:/Users/<USER>/OneDrive/Desktop/jadhagam/TamilJadhagamMobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.tamiljadhagam.mobile", "sourceDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\jadhagam\\TamilJadhagamMobile\\android"}}}