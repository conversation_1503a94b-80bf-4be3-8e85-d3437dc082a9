@echo off
REM Tamil Jadhagam Manager - Legacy System Build Script
REM தமிழ் ஜாதகம் மேலாண்மை - பழைய அமைப்பு கட்டமைப்பு ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - Legacy Build
echo தமிழ் ஜாதகம் மேலாண்மை - பழைய அமைப்பு கட்டமைப்பு
echo ========================================
echo.
echo Building for older systems like:
echo பழைய அமைப்புகளுக்கு கட்டமைக்கப்படுகிறது:
echo - AMD Athlon X2 processors
echo - Windows 7 32-bit systems
echo - Systems with limited resources
echo.

echo [1/3] Installing compatible Electron version...
echo இணக்கமான Electron பதிப்பை நிறுவுகிறது...
echo.

REM Install compatible Electron version
call npm install electron@13.6.9 --save-dev --no-optional
if %errorlevel% neq 0 (
    echo ✗ Failed to install compatible Electron version
    echo ✗ இணக்கமான Electron பதிப்பை நிறுவ முடியவில்லை
    pause
    exit /b 1
)

echo ✓ Compatible Electron version installed
echo ✓ இணக்கமான Electron பதிப்பு நிறுவப்பட்டது
echo.

echo [2/3] Building legacy-compatible package...
echo பழைய அமைப்பு இணக்கமான தொகுப்பை கட்டமைக்கிறது...
echo.

REM Build with legacy configuration
call npx electron-forge package --config=./forge.config.legacy.js
if %errorlevel% neq 0 (
    echo ✗ Legacy package build failed
    echo ✗ பழைய அமைப்பு தொகுப்பு கட்டமைப்பு தோல்வியடைந்தது
    pause
    exit /b 1
)

echo ✓ Legacy package built successfully
echo ✓ பழைய அமைப்பு தொகுப்பு வெற்றிகரமாக கட்டமைக்கப்பட்டது
echo.

echo [3/3] Creating legacy installer...
echo பழைய அமைப்பு நிறுவியை உருவாக்குகிறது...
echo.

REM Create installer with legacy configuration
call npx electron-forge make --config=./forge.config.legacy.js
if %errorlevel% neq 0 (
    echo ✗ Legacy installer creation failed
    echo ✗ பழைய அமைப்பு நிறுவி உருவாக்கம் தோல்வியடைந்தது
    pause
    exit /b 1
)

echo ✓ Legacy installer created successfully
echo ✓ பழைய அமைப்பு நிறுவி வெற்றிகரமாக உருவாக்கப்பட்டது
echo.

echo ========================================
echo LEGACY BUILD COMPLETED SUCCESSFULLY!
echo பழைய அமைப்பு கட்டமைப்பு வெற்றிகரமாக முடிந்தது!
echo ========================================
echo.

echo Available legacy installer:
echo கிடைக்கும் பழைய அமைப்பு நிறுவி:
echo.

if exist "out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-Legacy-Setup.exe" (
    echo ✓ Legacy 32-bit: out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-Legacy-Setup.exe
    for %%A in ("out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-Legacy-Setup.exe") do echo   Size: %%~zA bytes
) else (
    echo ⚠ Legacy installer not found in expected location
    echo ⚠ பழைய அமைப்பு நிறுவி எதிர்பார்த்த இடத்தில் இல்லை
    echo.
    echo Checking alternative locations...
    echo மாற்று இடங்களை சரிபார்க்கிறது...
    dir /s "out\*.exe" 2>nul
)

echo.
echo LEGACY SYSTEM COMPATIBILITY:
echo பழைய அமைப்பு இணக்கத்தன்மை:
echo.
echo ✓ AMD Athlon X2 250 and similar processors
echo ✓ Windows 7 32-bit and 64-bit
echo ✓ Systems with 1GB+ RAM
echo ✓ Older graphics cards and drivers
echo ✓ Limited system resources
echo.
echo INSTALLATION NOTES FOR LEGACY SYSTEMS:
echo பழைய அமைப்புகளுக்கான நிறுவல் குறிப்புகள்:
echo.
echo • Use Tamil-Jadhagam-Manager-Legacy-Setup.exe for older systems
echo • Requires Windows 7 or later
echo • Works with limited system resources
echo • Compatible with older processors
echo • No administrator rights required
echo.
echo • பழைய அமைப்புகளுக்கு Tamil-Jadhagam-Manager-Legacy-Setup.exe ஐ பயன்படுத்தவும்
echo • Windows 7 அல்லது அதற்கு பிந்தைய பதிப்பு தேவை
echo • குறைந்த அமைப்பு வளங்களுடன் வேலை செய்கிறது
echo • பழைய செயலிகளுடன் இணக்கமானது
echo • நிர்வாக உரிமைகள் தேவையில்லை
echo.

echo ========================================
echo Tamil Jadhagam Manager v1.3.3 Legacy
echo தமிழ் ஜாதகம் மேலாண்மை v1.3.3 பழைய அமைப்பு
echo Legacy System Support Ready!
echo பழைய அமைப்பு ஆதரவு தயார்!
echo ========================================
pause
