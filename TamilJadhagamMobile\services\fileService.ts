import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';

export interface FileResult {
  success: boolean;
  file?: {
    uri: string;
    name: string;
    size: number;
    type: string;
  };
  message?: string;
}

class FileService {
  private documentsDir: string;
  private photosDir: string;

  constructor() {
    this.documentsDir = `${FileSystem.documentDirectory}documents/`;
    this.photosDir = `${FileSystem.documentDirectory}photos/`;
    this.initializeDirectories();
  }

  private async initializeDirectories(): Promise<void> {
    try {
      // Create documents directory
      const documentsInfo = await FileSystem.getInfoAsync(this.documentsDir);
      if (!documentsInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.documentsDir, { intermediates: true });
      }

      // Create photos directory
      const photosInfo = await FileSystem.getInfoAsync(this.photosDir);
      if (!photosInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.photosDir, { intermediates: true });
      }
    } catch (error) {
      console.error('Error initializing directories:', error);
    }
  }

  async selectDocument(): Promise<FileResult> {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'image/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return { success: false, message: 'Document selection cancelled' };
      }

      const asset = result.assets[0];
      const fileName = `doc_${Date.now()}_${asset.name}`;
      const destinationUri = `${this.documentsDir}${fileName}`;

      // Copy file to app's document directory
      await FileSystem.copyAsync({
        from: asset.uri,
        to: destinationUri,
      });

      return {
        success: true,
        file: {
          uri: destinationUri,
          name: fileName,
          size: asset.size || 0,
          type: asset.mimeType || 'application/octet-stream',
        },
      };
    } catch (error) {
      console.error('Error selecting document:', error);
      return { success: false, message: `Document selection failed: ${error}` };
    }
  }

  async selectPhoto(): Promise<FileResult> {
    try {
      // Request permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        return { success: false, message: 'Permission to access media library denied' };
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 4],
        quality: 0.8,
      });

      if (result.canceled) {
        return { success: false, message: 'Photo selection cancelled' };
      }

      const asset = result.assets[0];
      const fileName = `photo_${Date.now()}.jpg`;
      const destinationUri = `${this.photosDir}${fileName}`;

      // Copy file to app's photos directory
      await FileSystem.copyAsync({
        from: asset.uri,
        to: destinationUri,
      });

      return {
        success: true,
        file: {
          uri: destinationUri,
          name: fileName,
          size: 0, // Size not available from ImagePicker
          type: 'image/jpeg',
        },
      };
    } catch (error) {
      console.error('Error selecting photo:', error);
      return { success: false, message: `Photo selection failed: ${error}` };
    }
  }

  async takePhoto(): Promise<FileResult> {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        return { success: false, message: 'Permission to access camera denied' };
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [3, 4],
        quality: 0.8,
      });

      if (result.canceled) {
        return { success: false, message: 'Photo capture cancelled' };
      }

      const asset = result.assets[0];
      const fileName = `photo_${Date.now()}.jpg`;
      const destinationUri = `${this.photosDir}${fileName}`;

      // Copy file to app's photos directory
      await FileSystem.copyAsync({
        from: asset.uri,
        to: destinationUri,
      });

      return {
        success: true,
        file: {
          uri: destinationUri,
          name: fileName,
          size: 0, // Size not available from ImagePicker
          type: 'image/jpeg',
        },
      };
    } catch (error) {
      console.error('Error taking photo:', error);
      return { success: false, message: `Photo capture failed: ${error}` };
    }
  }

  async scanDocument(): Promise<FileResult> {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        return { success: false, message: 'Permission to access camera denied' };
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [3, 4],
        quality: 1.0, // High quality for document scanning
      });

      if (result.canceled) {
        return { success: false, message: 'Document scan cancelled' };
      }

      const asset = result.assets[0];
      const fileName = `scan_${Date.now()}.jpg`;
      const destinationUri = `${this.documentsDir}${fileName}`;

      // Copy file to app's documents directory
      await FileSystem.copyAsync({
        from: asset.uri,
        to: destinationUri,
      });

      return {
        success: true,
        file: {
          uri: destinationUri,
          name: fileName,
          size: 0, // Size not available from ImagePicker
          type: 'image/jpeg',
        },
        message: 'Document scanned successfully',
      };
    } catch (error) {
      console.error('Error scanning document:', error);
      return { success: false, message: `Document scan failed: ${error}` };
    }
  }

  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(filePath);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(filePath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  async shareFile(filePath: string, fileName: string): Promise<boolean> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(filePath);
      if (!fileInfo.exists) {
        return false;
      }

      // Request media library permissions for saving
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        console.log('Permission to access media library denied');
        return false;
      }

      // Save to media library
      await MediaLibrary.saveToLibraryAsync(filePath);
      return true;
    } catch (error) {
      console.error('Error sharing file:', error);
      return false;
    }
  }

  async getFileInfo(filePath: string): Promise<FileSystem.FileInfo | null> {
    try {
      return await FileSystem.getInfoAsync(filePath);
    } catch (error) {
      console.error('Error getting file info:', error);
      return null;
    }
  }

  getDocumentsDirectory(): string {
    return this.documentsDir;
  }

  getPhotosDirectory(): string {
    return this.photosDir;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export const fileService = new FileService();
