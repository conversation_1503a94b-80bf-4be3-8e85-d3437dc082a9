const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Database operations
  getRasiList: () => ipcRenderer.invoke('get-rasi-list'),
  getNathathiramList: () => ipcRenderer.invoke('get-nathathiram-list'),
  getJadthiList: () => ipcRenderer.invoke('get-jadthi-list'),
  getCityList: () => ipcRenderer.invoke('get-city-list'),

  // Jadhagam operations
  uploadJadhagam: (data) => ipcRenderer.invoke('upload-jadhagam', data),
  searchJadhagam: (searchParams) => ipcRenderer.invoke('search-jadhagam', searchParams),
  openDocument: (filePath) => ipcRenderer.invoke('open-document', filePath),
  downloadDocument: (filePath, originalFileName) => ipcRenderer.invoke('download-document', filePath, originalFileName),
});
