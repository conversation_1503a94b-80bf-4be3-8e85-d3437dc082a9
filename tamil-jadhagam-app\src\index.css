/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #ff6b35;
  --primary-dark: #e55a2b;
  --primary-light: #ff8c66;
  --secondary-color: #2c3e50;
  --accent-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --light-bg: #f8f9fa;
  --dark-bg: #2c3e50;
  --border-color: #e1e8ed;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
  --border-radius: 12px;
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
}

body {
  font-family: 'Noto Sans Tamil', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  overflow-x: hidden;
}

#app {
  max-width: 1400px;
  margin: 20px auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  overflow: hidden;
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Header */
.header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 3rem 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 1;
}

.header-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: rotateIn 1s ease-out;
  color: rgba(255, 255, 255, 0.9);
}

.header h1 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 0.8s ease-out;
}

.header p {
  font-size: 1.3rem;
  opacity: 0.95;
  font-weight: 300;
  animation: fadeInUp 0.8s ease-out 0.2s both;
  margin-bottom: 1rem;
}

.header-subtitle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  opacity: 0.8;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.header-subtitle i {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes rotateIn {
  from {
    transform: rotate(-180deg) scale(0);
    opacity: 0;
  }
  to {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Navigation tabs */
.nav-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-button {
  flex: 1;
  padding: 1.2rem 2rem;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-secondary);
  transition: all var(--transition-medium);
  border-bottom: 3px solid transparent;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.tab-button i {
  font-size: 1.3rem;
  transition: all var(--transition-medium);
}

.tab-button:hover i {
  transform: scale(1.2);
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
  transition: left var(--transition-medium);
}

.tab-button:hover::before {
  left: 100%;
}

.tab-button:hover {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 107, 53, 0.1));
  color: var(--primary-color);
  transform: translateY(-2px);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
  box-shadow: inset 0 -3px 0 var(--primary-color);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 3px;
  background: var(--primary-color);
  border-radius: 2px;
  animation: expandWidth 0.3s ease-out;
}

@keyframes expandWidth {
  from {
    width: 0%;
  }
  to {
    width: 60%;
  }
}

/* Tab content */
.tab-content {
  display: none;
  padding: 3rem 2rem;
  animation: fadeIn 0.4s ease-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form styles */
.form-container {
  max-width: 900px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  padding: 2.5rem;
  box-shadow: var(--shadow-light);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-container h2 {
  color: var(--text-primary);
  margin-bottom: 2.5rem;
  font-size: 2.2rem;
  text-align: center;
  font-weight: 700;
  position: relative;
}

.form-container h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: 2px;
}

.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  animation: slideInLeft 0.5s ease-out;
}

.form-row:nth-child(even) {
  animation: slideInRight 0.5s ease-out;
}

.form-group {
  flex: 1;
  position: relative;
}

.form-group.full-width {
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 0.8rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.1rem;
  transition: color var(--transition-fast);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem 1.2rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-family: inherit;
  background: rgba(255, 255, 255, 0.9);
  transition: all var(--transition-medium);
  position: relative;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.1);
  transform: translateY(-2px);
  background: white;
}

.form-group input:focus + label,
.form-group select:focus + label,
.form-group textarea:focus + label {
  color: var(--primary-color);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Floating label effect */
.form-group.floating {
  position: relative;
}

.form-group.floating input,
.form-group.floating select {
  padding-top: 1.5rem;
}

.form-group.floating label {
  position: absolute;
  top: 1rem;
  left: 1.2rem;
  background: white;
  padding: 0 0.5rem;
  transition: all var(--transition-medium);
  pointer-events: none;
}

.form-group.floating input:focus + label,
.form-group.floating input:not(:placeholder-shown) + label,
.form-group.floating select:focus + label {
  top: -0.5rem;
  font-size: 0.9rem;
  color: var(--primary-color);
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Buttons */
.form-actions {
  text-align: center;
  margin-top: 3rem;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.btn-primary,
.btn-secondary {
  padding: 1rem 2.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-medium);
  margin: 0 0.8rem;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
}

.btn-primary i,
.btn-secondary i {
  font-size: 1.2rem;
  transition: transform var(--transition-medium);
}

.btn-primary:hover i,
.btn-secondary:hover i {
  transform: scale(1.1);
}

.btn-primary::before,
.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-medium);
}

.btn-primary:hover::before,
.btn-secondary:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  box-shadow: var(--shadow-light);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
}

.btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--text-secondary), #5a6268);
  color: white;
  box-shadow: var(--shadow-light);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #5a6268, var(--text-secondary));
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

.btn-secondary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

/* Results */
.results-container {
  max-width: 1200px;
  margin: 0 auto;
}

.results-list {
  margin-top: 2rem;
  display: grid;
  gap: 1.5rem;
}

.result-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  padding: 2rem;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.5s ease-out;
}

.result-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary-color), var(--primary-light));
  transform: scaleY(0);
  transition: transform var(--transition-medium);
}

.result-item:hover::before {
  transform: scaleY(1);
}

.result-item:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-5px);
  border-color: rgba(255, 107, 53, 0.2);
  background: rgba(255, 255, 255, 0.95);
}

.result-item:nth-child(even) {
  animation-delay: 0.1s;
}

.result-item:nth-child(3n) {
  animation-delay: 0.2s;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(255, 107, 53, 0.1);
}

.result-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.result-date {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  background: rgba(255, 107, 53, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 107, 53, 0.2);
}

.result-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.result-detail {
  background: rgba(255, 255, 255, 0.6);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 53, 0.1);
  transition: all var(--transition-fast);
}

.result-detail:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-detail-label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.result-detail-value {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.1rem;
}

.result-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 107, 53, 0.1);
}

.btn-open,
.btn-download,
.btn-delete {
  color: white;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 120px;
}

.btn-open::before,
.btn-download::before,
.btn-delete::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-medium);
}

.btn-open:hover::before,
.btn-download:hover::before,
.btn-delete:hover::before {
  left: 100%;
}

.btn-open {
  background: linear-gradient(135deg, var(--success-color), #219a52);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-open:hover {
  background: linear-gradient(135deg, #219a52, var(--success-color));
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.btn-download {
  background: linear-gradient(135deg, var(--accent-color), #2980b9);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-download:hover {
  background: linear-gradient(135deg, #2980b9, var(--accent-color));
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.btn-delete {
  background: linear-gradient(135deg, var(--danger-color), #c0392b);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-delete:hover {
  background: linear-gradient(135deg, #c0392b, var(--danger-color));
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

.btn-open:active,
.btn-download:active,
.btn-delete:active {
  transform: translateY(-1px);
}

/* Loading and messages */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
  margin-bottom: 1.5rem;
  position: relative;
}

.spinner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 107, 53, 0.3);
  border-radius: 50%;
  animation: spin 0.5s linear infinite reverse;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
  }
  50% {
    box-shadow: 0 0 0 20px rgba(255, 107, 53, 0);
  }
}

.loading p {
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  text-align: center;
  animation: fadeInUp 0.5s ease-out 0.2s both;
}

.message {
  position: fixed;
  top: 30px;
  right: 30px;
  padding: 1.5rem 2rem;
  border-radius: var(--border-radius);
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  z-index: 1001;
  max-width: 450px;
  min-width: 300px;
  box-shadow: var(--shadow-heavy);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideInRight 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  position: relative;
  overflow: hidden;
}

.message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
}

.message::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
}

@keyframes slideInRight {
  from {
    transform: translateX(120%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

.message.success {
  background: linear-gradient(135deg, var(--success-color), #219a52);
}

.message.error {
  background: linear-gradient(135deg, var(--danger-color), #c0392b);
}

.message.info {
  background: linear-gradient(135deg, var(--accent-color), #2980b9);
}

.hidden {
  display: none !important;
}

.no-results {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: 4rem 2rem;
  font-size: 1.2rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: var(--border-radius);
  margin: 2rem 0;
  border: 2px dashed rgba(255, 107, 53, 0.3);
  animation: fadeIn 0.5s ease-out;
}

.no-results::before {
  content: '🔍';
  display: block;
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Professional scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--primary-color), var(--primary-dark));
  border-radius: 4px;
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--primary-dark), var(--primary-color));
}

/* Focus indicators for accessibility */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.tab-button:focus,
.btn-primary:focus,
.btn-secondary:focus,
.btn-open:focus,
.btn-download:focus,
.btn-delete:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.3);
}

/* Smooth transitions for all interactive elements */
* {
  transition: box-shadow var(--transition-fast);
}

/* Professional hover effects */
.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
  border-color: rgba(255, 107, 53, 0.5);
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
}

/* Enhanced card animations */
.result-item {
  animation-fill-mode: both;
}

.result-item:nth-child(1) { animation-delay: 0.1s; }
.result-item:nth-child(2) { animation-delay: 0.2s; }
.result-item:nth-child(3) { animation-delay: 0.3s; }
.result-item:nth-child(4) { animation-delay: 0.4s; }
.result-item:nth-child(5) { animation-delay: 0.5s; }

/* Micro-interactions */
.form-group label {
  cursor: pointer;
  transition: all var(--transition-fast);
}

.form-group label:hover {
  color: var(--primary-color);
}

/* Professional loading states */
.form-group input:disabled,
.form-group select:disabled,
.form-group textarea:disabled {
  background: rgba(0, 0, 0, 0.05);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Enhanced button states */
.btn-primary:disabled,
.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Glassmorphism effects */
.form-container,
.result-item {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Responsive design */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .nav-tabs {
    flex-direction: column;
  }

  .tab-button {
    border-bottom: 1px solid #e0e0e0;
    border-right: none;
  }

  .tab-button.active {
    border-bottom-color: #e0e0e0;
    border-left: 3px solid #ff6b35;
  }

  .result-details {
    grid-template-columns: 1fr;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .result-actions {
    justify-content: center;
    margin-top: 1rem;
  }

  .btn-open,
  .btn-download,
  .btn-delete {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}
