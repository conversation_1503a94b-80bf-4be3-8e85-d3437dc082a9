{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "React", "_interopRequireWildcard", "require", "_reactNative", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "UnimplementedView", "props", "createElement", "View", "style", "styles", "unimplementedView", "children", "StyleSheet", "create", "process", "env", "NODE_ENV", "alignSelf", "borderColor", "borderWidth", "_default"], "sourceRoot": "../../js", "sources": ["UnimplementedView.web.js"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAA8C,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAN,OAAA,EAAAM,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAE,GAAA,CAAAL,CAAA,UAAAG,CAAA,CAAAG,GAAA,CAAAN,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAnB,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAoB,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,IAAArB,MAAA,CAAAsB,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAW,CAAA,SAAAI,CAAA,GAAAN,CAAA,GAAAnB,MAAA,CAAAoB,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAT,GAAA,IAAAS,CAAA,CAAAC,GAAA,IAAA1B,MAAA,CAAAC,cAAA,CAAAgB,CAAA,EAAAI,CAAA,EAAAI,CAAA,IAAAR,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAb,OAAA,GAAAM,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAhB,CAAA,EAAAO,CAAA,GAAAA,CAAA;AAE9C;AACA;AACA;AACA;AACA,MAAMU,iBAAiB,GAAIC,KAAsB,IAAiB;EAChE,oBACEvB,KAAA,CAAAwB,aAAA,CAACrB,YAAA,CAAAsB,IAAI;IAACC,KAAK,EAAE,CAACC,MAAM,CAACC,iBAAiB,EAAEL,KAAK,CAACG,KAAK;EAAE,GAClDH,KAAK,CAACM,QACH,CAAC;AAEX,CAAC;AAED,MAAMF,MAAM,GAAGG,uBAAU,CAACC,MAAM,CAAC;EAC/BH,iBAAiB,EACfI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACjC;IACEC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACf,CAAC,GACD,CAAC;AACT,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAzC,OAAA,CAAAE,OAAA,GAEYuB,iBAAiB"}