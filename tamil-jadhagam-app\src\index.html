<!DOCTYPE html>
<html lang="ta">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>தமிழ் ஜாதகம் மேலாண்மை - Tamil Jadhagam Manager</title>
    <link rel="stylesheet" href="index.css" />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Tamil:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  </head>
  <body>
    <div id="app">
      <!-- Custom Title Bar -->
      <div class="custom-title-bar">
        <div class="title-bar-title">
          <span class="tamil-icon">ஜ</span>
          <span>தமிழ் ஜாதகம் மேலாண்மை - Tamil Jadhagam Manager v1.1.0</span>
          <small style="margin-left: 20px; opacity: 0.7; font-size: 11px;">
            F11: Toggle Fullscreen | Ctrl+M: Minimize | Ctrl+Q: Quit | Esc: Minimize
          </small>
        </div>
        <div class="title-bar-controls">
          <button class="title-bar-button minimize" onclick="app.minimizeWindow()" title="Minimize">
            <i class="fas fa-window-minimize"></i>
          </button>
          <button class="title-bar-button close" onclick="app.closeWindow()" title="Close">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- App Content -->
      <div class="app-content">
        <!-- Header -->
      <header class="header">
        <div class="header-content">
          <div class="header-icon">
            <span class="tamil-header-icon">ஜ</span>
          </div>
          <h1>தமிழ் ஜாதகம் மேலாண்மை</h1>
          <p>Tamil Jadhagam Management System</p>
          <div class="header-subtitle">
            <i class="fas fa-desktop"></i>
            <span>Professional Fullscreen Document Management</span>
          </div>
        </div>
      </header>

      <!-- Navigation -->
      <nav class="nav-tabs">
        <button class="tab-button active" data-tab="upload">
          <i class="fas fa-cloud-upload-alt"></i>
          <span>பதிவேற்றம் - Upload</span>
        </button>
        <button class="tab-button" data-tab="search">
          <i class="fas fa-search"></i>
          <span>தேடல் - Search</span>
        </button>
        <button class="tab-button" data-tab="results">
          <i class="fas fa-list-ul"></i>
          <span>முடிவுகள் - Results</span>
        </button>
      </nav>

      <!-- Upload Tab -->
      <div id="upload-tab" class="tab-content active">
        <div class="form-container">
          <h2>
            <i class="fas fa-plus-circle"></i>
            புதிய ஜாதகம் பதிவேற்றம் - New Jadhagam Upload
          </h2>
          <form id="upload-form">
            <div class="form-row">
              <div class="form-group">
                <label for="name">பெயர் - Name *</label>
                <input type="text" id="name" name="name" required>
              </div>
              <div class="form-group">
                <label for="gender">பாலினம் - Gender</label>
                <select id="gender" name="gender">
                  <option value="">தேர்ந்தெடுக்கவும் - Select</option>
                  <option value="ஆண் - Male">ஆண் - Male</option>
                  <option value="பெண் - Female">பெண் - Female</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="rasi">ராசி - Rasi *</label>
                <select id="rasi" name="rasi" required>
                  <option value="">தேர்ந்தெடுக்கவும் - Select Rasi</option>
                </select>
              </div>
              <div class="form-group">
                <label for="nathathiram">நட்சத்திரம் - Nathathiram *</label>
                <select id="nathathiram" name="nathathiram" required>
                  <option value="">தேர்ந்தெடுக்கவும் - Select Nathathiram</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="jadthi">ஜாதி - Jadthi *</label>
                <select id="jadthi" name="jadthi" required>
                  <option value="">தேர்ந்தெடுக்கவும் - Select Jadthi</option>
                </select>
              </div>
              <div class="form-group">
                <label for="city">நகரம் - City *</label>
                <select id="city" name="city" required>
                  <option value="">தேர்ந்தெடுக்கவும் - Select City</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="birth-date">பிறந்த தேதி - Birth Date</label>
                <input type="date" id="birth-date" name="birth_date">
              </div>
              <div class="form-group">
                <label for="birth-time">பிறந்த நேரம் - Birth Time</label>
                <input type="time" id="birth-time" name="birth_time">
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="father-name">தந்தையின் பெயர் - Father's Name</label>
                <input type="text" id="father-name" name="father_name">
              </div>
              <div class="form-group">
                <label for="mother-name">தாயின் பெயர் - Mother's Name</label>
                <input type="text" id="mother-name" name="mother_name">
              </div>
            </div>

            <div class="form-group full-width">
              <label for="notes">குறிப்புகள் - Notes</label>
              <textarea id="notes" name="notes" rows="3"></textarea>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn-primary">
                <i class="fas fa-upload"></i>
                ஜாதகம் பதிவேற்றம் - Upload Jadhagam
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Search Tab -->
      <div id="search-tab" class="tab-content">
        <div class="form-container">
          <h2>
            <i class="fas fa-search"></i>
            ஜாதகம் தேடல் - Search Jadhagam
          </h2>
          <form id="search-form">
            <div class="form-row">
              <div class="form-group">
                <label for="search-name">பெயர் - Name</label>
                <input type="text" id="search-name" name="name" placeholder="பெயரை உள்ளிடவும் - Enter name">
              </div>
              <div class="form-group">
                <label for="search-rasi">ராசி - Rasi</label>
                <select id="search-rasi" name="rasi">
                  <option value="">அனைத்தும் - All</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="search-nathathiram">நட்சத்திரம் - Nathathiram</label>
                <select id="search-nathathiram" name="nathathiram">
                  <option value="">அனைத்தும் - All</option>
                </select>
              </div>
              <div class="form-group">
                <label for="search-jadthi">ஜாதி - Jadthi</label>
                <select id="search-jadthi" name="jadthi">
                  <option value="">அனைத்தும் - All</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="search-city">நகரம் - City</label>
                <select id="search-city" name="city">
                  <option value="">அனைத்தும் - All</option>
                </select>
              </div>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn-primary">
                <i class="fas fa-search"></i>
                தேடல் - Search
              </button>
              <button type="button" id="clear-search" class="btn-secondary">
                <i class="fas fa-eraser"></i>
                அழிக்க - Clear
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Results Tab -->
      <div id="results-tab" class="tab-content">
        <div class="results-container">
          <h2>
            <i class="fas fa-list-alt"></i>
            தேடல் முடிவுகள் - Search Results
          </h2>
          <div id="results-list" class="results-list">
            <p class="no-results">தேடல் முடிவுகள் இல்லை - No search results</p>
          </div>
        </div>
      </div>

      <!-- Loading indicator -->
      <div id="loading" class="loading hidden">
        <div class="spinner"></div>
        <p>
          <i class="fas fa-cog fa-spin"></i>
          செயலாக்கப்படுகிறது... - Processing...
        </p>
      </div>

        <!-- Message display -->
        <div id="message" class="message hidden"></div>
      </div> <!-- End app-content -->
    </div> <!-- End app -->

    <script src="app.js"></script>
  </body>
</html>
