@echo off
REM Tamil Jadhagam Manager - Deployment Script
REM தமிழ் ஜாதகம் மேலாண்மை - வரிசைப்படுத்தல் ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - Deployment
echo தமிழ் ஜாதகம் மேலாண்மை - வரிசைப்படுத்தல்
echo ========================================
echo.

REM Check if Node.js is installed
echo [1/6] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js is installed

REM Check if npm is available
echo [2/6] Checking npm availability...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    pause
    exit /b 1
)
echo ✓ npm is available

REM Install dependencies
echo [3/6] Installing dependencies...
echo This may take a few minutes...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo ✓ Dependencies installed successfully

REM Build the application
echo [4/6] Building the application...
echo This may take a few minutes...
npm run build
if %errorlevel% neq 0 (
    echo ERROR: Failed to build application
    pause
    exit /b 1
)
echo ✓ Application built successfully

REM Create installer
echo [5/6] Creating Windows installer...
echo This may take several minutes...
npm run dist
if %errorlevel% neq 0 (
    echo ERROR: Failed to create installer
    pause
    exit /b 1
)
echo ✓ Installer created successfully

REM Display results
echo [6/6] Deployment completed successfully!
echo.
echo ========================================
echo DEPLOYMENT RESULTS
echo ========================================
echo.
echo Installer Location:
echo %CD%\out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe
echo.
echo Package Location:
echo %CD%\out\make\squirrel.windows\x64\tamil-jadhagam-manager-1.0.0-full.nupkg
echo.
echo Application Build:
echo %CD%\out\Tamil Jadhagam Manager-win32-x64\
echo.
echo ========================================
echo NEXT STEPS
echo ========================================
echo.
echo 1. Test the installer:
echo    - Run Tamil-Jadhagam-Manager-Setup.exe as administrator
echo    - Verify installation completes successfully
echo    - Test the application functionality
echo.
echo 2. Distribute the installer:
echo    - Share Tamil-Jadhagam-Manager-Setup.exe with users
echo    - Provide INSTALLATION_GUIDE.md for reference
echo    - Include USER_MANUAL.md for user guidance
echo.
echo 3. Documentation available:
echo    - README.md - Project overview
echo    - INSTALLATION_GUIDE.md - Installation instructions
echo    - USER_MANUAL.md - User guide
echo    - PROJECT_SUMMARY.md - Technical summary
echo.
echo ========================================
echo Tamil Jadhagam Manager v1.0.0
echo தமிழ் ஜாதகம் மேலாண்மை v1.0.0
echo Deployment completed successfully!
echo ========================================
echo.
pause
