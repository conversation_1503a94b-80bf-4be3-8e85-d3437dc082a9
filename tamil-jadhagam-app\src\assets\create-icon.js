// Node.js script to create PNG icon from SVG
const fs = require('fs');
const path = require('path');

// Simple SVG to create a basic icon
const createSimpleIcon = (size) => {
    const svg = `
<svg width="${size}" height="${size}" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b35"/>
      <stop offset="100%" style="stop-color:#e55a2b"/>
    </linearGradient>
  </defs>
  <circle cx="128" cy="128" r="120" fill="url(#bg)" stroke="#d4491f" stroke-width="3"/>
  <text x="128" y="160" text-anchor="middle" font-family="serif" font-size="120" font-weight="bold" fill="white">ஜ</text>
  <text x="128" y="220" text-anchor="middle" fill="white" font-family="serif" font-size="16" opacity="0.9">ஜாதகம்</text>
</svg>`;
    return svg;
};

// Create icon files
const sizes = [16, 32, 48, 64, 128, 256];

console.log('Creating Tamil Jadhagam Manager Icons...');
console.log('=====================================');

sizes.forEach(size => {
    const svg = createSimpleIcon(size);
    const filename = `app-icon-${size}.svg`;
    const filepath = path.join(__dirname, filename);
    
    fs.writeFileSync(filepath, svg);
    console.log(`✓ Created ${filename}`);
});

console.log('\nIcon files created successfully!');
console.log('\nTo convert to PNG:');
console.log('1. Open icon-converter.html in a web browser');
console.log('2. Download the PNG files');
console.log('3. Rename the 256x256 version to "app-icon.png"');
console.log('4. Use online tools to convert to ICO format for Windows');

console.log('\nTamil Letter "ஜ" represents:');
console.log('- ஜாதகம் (Jadhagam) - Horoscope');
console.log('- Professional Tamil software branding');
console.log('- Cultural authenticity and recognition');

// Create a simple fallback icon data
const iconInfo = {
    name: 'Tamil Jadhagam Manager',
    letter: 'ஜ',
    meaning: 'Jadhagam (Horoscope)',
    colors: {
        primary: '#ff6b35',
        secondary: '#e55a2b',
        text: '#ffffff'
    },
    sizes: sizes,
    formats: ['SVG', 'PNG', 'ICO'],
    usage: 'Desktop application icon for Tamil Jadhagam document management'
};

fs.writeFileSync(path.join(__dirname, 'icon-info.json'), JSON.stringify(iconInfo, null, 2));
console.log('✓ Created icon-info.json with metadata');
