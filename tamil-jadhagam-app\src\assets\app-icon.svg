<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f7931e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e55a2b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="letterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f8f9fa;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.9" />
    </linearGradient>
    <filter id="letterShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000000" flood-opacity="0.4"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background circle with subtle gradient -->
  <circle cx="128" cy="128" r="120" fill="url(#bgGradient)" stroke="#d4491f" stroke-width="3"/>

  <!-- Inner circle for depth -->
  <circle cx="128" cy="128" r="110" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>

  <!-- Outer decorative ring -->
  <circle cx="128" cy="128" r="100" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" stroke-dasharray="8,4">
    <animateTransform attributeName="transform" type="rotate" values="0 128 128;360 128 128" dur="30s" repeatCount="indefinite"/>
  </circle>

  <!-- Tamil Letter "ஜ" (J for Jadhagam) -->
  <g transform="translate(128,128)" filter="url(#letterShadow)">
    <!-- Main Tamil letter "ஜ" styled professionally -->
    <text x="0" y="20"
          text-anchor="middle"
          font-family="Noto Sans Tamil, Tamil Sangam MN, Latha, serif"
          font-size="120"
          font-weight="bold"
          fill="url(#letterGradient)"
          filter="url(#glow)">ஜ</text>
  </g>

  <!-- Decorative corner elements -->
  <g opacity="0.7">
    <!-- Corner stars -->
    <g transform="translate(45,45)">
      <path d="M 0 -6 L 2 -2 L 6 -2 L 3 1 L 4 6 L 0 3 L -4 6 L -3 1 L -6 -2 L -2 -2 Z" fill="#ffffff"/>
    </g>
    <g transform="translate(211,45)">
      <path d="M 0 -6 L 2 -2 L 6 -2 L 3 1 L 4 6 L 0 3 L -4 6 L -3 1 L -6 -2 L -2 -2 Z" fill="#ffffff"/>
    </g>
    <g transform="translate(45,211)">
      <path d="M 0 -6 L 2 -2 L 6 -2 L 3 1 L 4 6 L 0 3 L -4 6 L -3 1 L -6 -2 L -2 -2 Z" fill="#ffffff"/>
    </g>
    <g transform="translate(211,211)">
      <path d="M 0 -6 L 2 -2 L 6 -2 L 3 1 L 4 6 L 0 3 L -4 6 L -3 1 L -6 -2 L -2 -2 Z" fill="#ffffff"/>
    </g>
  </g>

  <!-- Bottom text -->
  <g transform="translate(128,220)" text-anchor="middle" fill="#ffffff" font-family="Noto Sans Tamil, serif" font-weight="600">
    <text font-size="14" opacity="0.9">ஜாதகம்</text>
  </g>

  <!-- Subtle inner glow -->
  <circle cx="128" cy="128" r="85" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="1"/>

  <!-- Professional border -->
  <circle cx="128" cy="128" r="123" fill="none" stroke="rgba(0,0,0,0.1)" stroke-width="1"/>
</svg>
