<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f7931e;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:0.9" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="url(#bgGradient)" stroke="#e55a2b" stroke-width="4"/>
  
  <!-- Outer decorative ring -->
  <circle cx="128" cy="128" r="100" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" stroke-dasharray="5,5">
    <animateTransform attributeName="transform" type="rotate" values="0 128 128;360 128 128" dur="20s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Star and Crescent (Islamic/Tamil astronomical symbol) -->
  <g transform="translate(128,128)" filter="url(#shadow)">
    <!-- Crescent -->
    <path d="M -25 -15 A 20 20 0 1 1 -25 15 A 15 15 0 1 0 -25 -15 Z" fill="url(#starGradient)" stroke="#ffffff" stroke-width="1"/>
    
    <!-- Star -->
    <g transform="translate(15,-5)">
      <path d="M 0 -12 L 3 -3 L 12 -3 L 6 3 L 9 12 L 0 6 L -9 12 L -6 3 L -12 -3 L -3 -3 Z" fill="url(#starGradient)" stroke="#ffffff" stroke-width="1"/>
    </g>
  </g>
  
  <!-- Tamil text styling -->
  <g transform="translate(128,200)" text-anchor="middle" fill="#ffffff" font-family="serif" font-weight="bold">
    <text font-size="16" opacity="0.9">ஜாதகம்</text>
  </g>
  
  <!-- Decorative elements -->
  <g opacity="0.6">
    <!-- Corner decorations -->
    <circle cx="40" cy="40" r="3" fill="#ffffff"/>
    <circle cx="216" cy="40" r="3" fill="#ffffff"/>
    <circle cx="40" cy="216" r="3" fill="#ffffff"/>
    <circle cx="216" cy="216" r="3" fill="#ffffff"/>
    
    <!-- Side decorations -->
    <circle cx="20" cy="128" r="2" fill="#ffffff"/>
    <circle cx="236" cy="128" r="2" fill="#ffffff"/>
    <circle cx="128" cy="20" r="2" fill="#ffffff"/>
    <circle cx="128" cy="236" r="2" fill="#ffffff"/>
  </g>
  
  <!-- Inner glow effect -->
  <circle cx="128" cy="128" r="90" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
</svg>
