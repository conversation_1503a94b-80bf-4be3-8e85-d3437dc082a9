{"expo": {"name": "Tamil Jadhagam Manager", "slug": "tamil-jadhagam-mobile", "version": "1.0.0", "description": "Tamil Jadhagam Document Management System - தமிழ் ஜாதகம் ஆவண மேலாண்மை அமைப்பு", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "ta<PERSON><PERSON><PERSON><PERSON>gam", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.tamiljadhagam.mobile", "buildNumber": "1.0.0", "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to scan documents and take photos for jadhagam records.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select photos for jadhagam records.", "NSDocumentsFolderUsageDescription": "This app needs access to documents to manage jadhagam files."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.tamiljadhagam.mobile", "versionCode": 1, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_MEDIA_IMAGES", "android.permission.READ_MEDIA_VIDEO"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-sqlite", "expo-document-picker", "expo-image-picker", "expo-camera", "expo-media-library", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}