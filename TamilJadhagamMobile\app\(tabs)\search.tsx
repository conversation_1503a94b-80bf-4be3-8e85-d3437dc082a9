import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  Image,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { databaseService, JadhagamDocument, RasiItem, NathathiramItem } from '@/services/database';
import { fileService } from '@/services/fileService';

export default function SearchScreen() {
  const [searchParams, setSearchParams] = useState<Partial<JadhagamDocument>>({
    name: '',
    gender: '',
    rasi: '',
    nathathiram: '',
    jathi: '',
    city: '',
    dosham: '',
  });

  const [rasiList, setRasiList] = useState<RasiItem[]>([]);
  const [nathathiramList, setNathathiramList] = useState<NathathiramItem[]>([]);
  const [searchResults, setSearchResults] = useState<JadhagamDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    loadDropdownData();
  }, []);

  const loadDropdownData = async () => {
    try {
      const [rasis, nathathirams] = await Promise.all([
        databaseService.getRasiList(),
        databaseService.getNathathiramList(),
      ]);
      setRasiList(rasis);
      setNathathiramList(nathathirams);
    } catch (error) {
      console.error('Error loading dropdown data:', error);
      Alert.alert('பிழை', 'தரவு ஏற்றுவதில் பிழை ஏற்பட்டது');
    }
  };

  const handleInputChange = (field: keyof JadhagamDocument, value: string) => {
    setSearchParams(prev => ({ ...prev, [field]: value }));
  };

  const handleSearch = async () => {
    setLoading(true);
    try {
      const results = await databaseService.searchJadhagamDocuments(searchParams);
      setSearchResults(results);
      setShowResults(true);
      
      if (results.length === 0) {
        Alert.alert('தகவல்', 'தேடல் முடிவுகள் எதுவும் கிடைக்கவில்லை');
      }
    } catch (error) {
      console.error('Error searching documents:', error);
      Alert.alert('பிழை', 'தேடலில் பிழை ஏற்பட்டது');
    } finally {
      setLoading(false);
    }
  };

  const clearSearch = () => {
    setSearchParams({
      name: '',
      gender: '',
      rasi: '',
      nathathiram: '',
      jathi: '',
      city: '',
      dosham: '',
    });
    setSearchResults([]);
    setShowResults(false);
  };

  const handleDocumentAction = async (document: JadhagamDocument, action: 'view' | 'share' | 'delete') => {
    switch (action) {
      case 'view':
        Alert.alert(
          'ஆவண விவரங்கள் - Document Details',
          `பெயர்: ${document.name}\nபாலினம்: ${document.gender}\nராசி: ${document.rasi}\nநட்சத்திரம்: ${document.nathathiram}\nஜாதி: ${document.jathi || 'N/A'}\nஊர்: ${document.city || 'N/A'}\nதோஷம்: ${document.dosham || 'N/A'}\nபதிவேற்ற தேதி: ${document.upload_date}`
        );
        break;
      
      case 'share':
        if (document.document_path) {
          const success = await fileService.shareFile(document.document_path, document.document_name || 'document');
          if (success) {
            Alert.alert('வெற்றி', 'ஆவணம் பகிரப்பட்டது');
          } else {
            Alert.alert('பிழை', 'ஆவணம் பகிர முடியவில்லை');
          }
        }
        break;
      
      case 'delete':
        Alert.alert(
          'உறுதிப்படுத்தல்',
          'இந்த ஆவணத்தை நிச்சயமாக நீக்க விரும்புகிறீர்களா?',
          [
            { text: 'ரத்து', style: 'cancel' },
            {
              text: 'நீக்கு',
              style: 'destructive',
              onPress: async () => {
                try {
                  if (document.id) {
                    await databaseService.deleteJadhagamDocument(document.id);
                    // Remove from local results
                    setSearchResults(prev => prev.filter(item => item.id !== document.id));
                    Alert.alert('வெற்றி', 'ஆவணம் நீக்கப்பட்டது');
                  }
                } catch (error) {
                  Alert.alert('பிழை', 'ஆவணம் நீக்குவதில் பிழை ஏற்பட்டது');
                }
              },
            },
          ]
        );
        break;
    }
  };

  const renderSearchResult = ({ item }: { item: JadhagamDocument }) => (
    <View style={styles.resultCard}>
      <View style={styles.resultHeader}>
        <View style={styles.resultInfo}>
          <Text style={styles.resultName}>{item.name}</Text>
          <Text style={styles.resultDetails}>
            {item.gender} • {item.rasi} • {item.nathathiram}
          </Text>
          {item.jathi && <Text style={styles.resultSubDetails}>ஜாதி: {item.jathi}</Text>}
          {item.city && <Text style={styles.resultSubDetails}>ஊர்: {item.city}</Text>}
          {item.dosham && <Text style={styles.resultSubDetails}>தோஷம்: {item.dosham}</Text>}
        </View>
        {item.photo_path && (
          <Image source={{ uri: item.photo_path }} style={styles.resultPhoto} />
        )}
      </View>
      
      <View style={styles.resultActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.viewButton]}
          onPress={() => handleDocumentAction(item, 'view')}
        >
          <Text style={styles.actionButtonText}>பார்க்க</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.shareButton]}
          onPress={() => handleDocumentAction(item, 'share')}
        >
          <Text style={styles.actionButtonText}>பகிர்</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDocumentAction(item, 'delete')}
        >
          <Text style={styles.actionButtonText}>நீக்கு</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>தேடல் - Search</Text>
        <Text style={styles.headerSubtitle}>ஜாதக ஆவணங்களைத் தேடவும்</Text>
      </View>

      <ScrollView style={styles.searchForm}>
        <View style={styles.form}>
          <Text style={styles.sectionTitle}>தேடல் அளவுகோல்கள் - Search Criteria</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>பெயர் - Name</Text>
            <TextInput
              style={styles.input}
              value={searchParams.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="பெயரை உள்ளிடவும் - Enter name"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>பாலினம் - Gender</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={searchParams.gender}
                onValueChange={(value) => handleInputChange('gender', value)}
                style={styles.picker}
              >
                <Picker.Item label="அனைத்தும் - All" value="" />
                <Picker.Item label="ஆண் - Male" value="ஆண் - Male" />
                <Picker.Item label="பெண் - Female" value="பெண் - Female" />
              </Picker>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>ராசி - Rasi</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={searchParams.rasi}
                onValueChange={(value) => handleInputChange('rasi', value)}
                style={styles.picker}
              >
                <Picker.Item label="அனைத்தும் - All" value="" />
                {rasiList.map((rasi) => (
                  <Picker.Item
                    key={rasi.id}
                    label={`${rasi.rasi_tamil} - ${rasi.rasi_english}`}
                    value={`${rasi.rasi_tamil} - ${rasi.rasi_english}`}
                  />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>நட்சத்திரம் - Nathathiram</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={searchParams.nathathiram}
                onValueChange={(value) => handleInputChange('nathathiram', value)}
                style={styles.picker}
              >
                <Picker.Item label="அனைத்தும் - All" value="" />
                {nathathiramList.map((nathathiram) => (
                  <Picker.Item
                    key={nathathiram.id}
                    label={`${nathathiram.nathathiram_tamil} - ${nathathiram.nathathiram_english}`}
                    value={`${nathathiram.nathathiram_tamil} - ${nathathiram.nathathiram_english}`}
                  />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>ஜாதி - Jathi/Caste</Text>
            <TextInput
              style={styles.input}
              value={searchParams.jathi}
              onChangeText={(value) => handleInputChange('jathi', value)}
              placeholder="ஜாதியை உள்ளிடவும் - Enter jathi"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>ஊர்/நகரம் - City/Town</Text>
            <TextInput
              style={styles.input}
              value={searchParams.city}
              onChangeText={(value) => handleInputChange('city', value)}
              placeholder="ஊரை உள்ளிடவும் - Enter city"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>தோஷம் - Dosham</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={searchParams.dosham}
                onValueChange={(value) => handleInputChange('dosham', value)}
                style={styles.picker}
              >
                <Picker.Item label="அனைத்தும் - All" value="" />
                <Picker.Item label="தோஷம் இல்லை - No Dosham" value="தோஷம் இல்லை - No Dosham" />
                <Picker.Item label="செவ்வாய் தோஷம் - Chevvai Dosham" value="செவ்வாய் தோஷம் - Chevvai Dosham" />
                <Picker.Item label="நாக தோஷம் - Naga Dosham" value="நாக தோஷம் - Naga Dosham" />
                <Picker.Item label="சுத்த ஜாதகம் - Sutha Jadhagam" value="சுத்த ஜாதகம் - Sutha Jadhagam" />
              </Picker>
            </View>
          </View>

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.searchButton, loading && styles.disabledButton]}
              onPress={handleSearch}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.searchButtonText}>தேடல் - Search</Text>
              )}
            </TouchableOpacity>
            <TouchableOpacity style={styles.clearButton} onPress={clearSearch}>
              <Text style={styles.clearButtonText}>அழிக்க - Clear</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {showResults && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>
            தேடல் முடிவுகள் - Search Results ({searchResults.length})
          </Text>
          <FlatList
            data={searchResults}
            renderItem={renderSearchResult}
            keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
            style={styles.resultsList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4CAF50',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  searchForm: {
    maxHeight: '50%',
  },
  form: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    borderBottomWidth: 2,
    borderBottomColor: '#4CAF50',
    paddingBottom: 5,
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  picker: {
    height: 40,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 10,
  },
  searchButton: {
    flex: 2,
    backgroundColor: '#4CAF50',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButton: {
    flex: 1,
    backgroundColor: '#FF5722',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  searchButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    marginTop: 10,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  resultsList: {
    flex: 1,
  },
  resultCard: {
    backgroundColor: '#fff',
    margin: 10,
    padding: 15,
    borderRadius: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  resultInfo: {
    flex: 1,
  },
  resultName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  resultDetails: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  resultSubDetails: {
    fontSize: 12,
    color: '#888',
    marginBottom: 2,
  },
  resultPhoto: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginLeft: 10,
  },
  resultActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    padding: 8,
    borderRadius: 6,
    alignItems: 'center',
  },
  viewButton: {
    backgroundColor: '#2196F3',
  },
  shareButton: {
    backgroundColor: '#FF9800',
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});
