import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { databaseService, JadhagamDocument, RasiItem, NathathiramItem } from '@/services/database';
import { fileService, FileResult } from '@/services/fileService';

export default function UploadScreen() {
  const [formData, setFormData] = useState<Partial<JadhagamDocument>>({
    name: '',
    gender: '',
    rasi: '',
    nathathiram: '',
    jathi: '',
    city: '',
    birth_place: '',
    dosham: '',
  });

  const [rasiList, setRasiList] = useState<RasiItem[]>([]);
  const [nathathiramList, setNathathiramList] = useState<NathathiramItem[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<FileResult['file'] | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<FileResult['file'] | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDropdownData();
  }, []);

  const loadDropdownData = async () => {
    try {
      const [rasis, nathathirams] = await Promise.all([
        databaseService.getRasiList(),
        databaseService.getNathathiramList(),
      ]);
      setRasiList(rasis);
      setNathathiramList(nathathirams);
    } catch (error) {
      console.error('Error loading dropdown data:', error);
      Alert.alert('பிழை', 'தரவு ஏற்றுவதில் பிழை ஏற்பட்டது');
    }
  };

  const handleInputChange = (field: keyof JadhagamDocument, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const selectDocument = async () => {
    try {
      const result = await fileService.selectDocument();
      if (result.success && result.file) {
        setSelectedDocument(result.file);
        Alert.alert('வெற்றி', 'ஆவணம் தேர்ந்தெடுக்கப்பட்டது');
      } else {
        Alert.alert('பிழை', result.message || 'ஆவணம் தேர்ந்தெடுக்க முடியவில்லை');
      }
    } catch (error) {
      Alert.alert('பிழை', 'ஆவணம் தேர்ந்தெடுக்கும்போது பிழை ஏற்பட்டது');
    }
  };

  const scanDocument = async () => {
    try {
      const result = await fileService.scanDocument();
      if (result.success && result.file) {
        setSelectedDocument(result.file);
        Alert.alert('வெற்றி', 'ஆவணம் ஸ்கேன் செய்யப்பட்டது');
      } else {
        Alert.alert('பிழை', result.message || 'ஆவணம் ஸ்கேன் செய்ய முடியவில்லை');
      }
    } catch (error) {
      Alert.alert('பிழை', 'ஆவணம் ஸ்கேன் செய்யும்போது பிழை ஏற்பட்டது');
    }
  };

  const selectPhoto = async () => {
    try {
      const result = await fileService.selectPhoto();
      if (result.success && result.file) {
        setSelectedPhoto(result.file);
        Alert.alert('வெற்றி', 'புகைப்படம் தேர்ந்தெடுக்கப்பட்டது');
      } else {
        Alert.alert('பிழை', result.message || 'புகைப்படம் தேர்ந்தெடுக்க முடியவில்லை');
      }
    } catch (error) {
      Alert.alert('பிழை', 'புகைப்படம் தேர்ந்தெடுக்கும்போது பிழை ஏற்பட்டது');
    }
  };

  const takePhoto = async () => {
    try {
      const result = await fileService.takePhoto();
      if (result.success && result.file) {
        setSelectedPhoto(result.file);
        Alert.alert('வெற்றி', 'புகைப்படம் எடுக்கப்பட்டது');
      } else {
        Alert.alert('பிழை', result.message || 'புகைப்படம் எடுக்க முடியவில்லை');
      }
    } catch (error) {
      Alert.alert('பிழை', 'புகைப்படம் எடுக்கும்போது பிழை ஏற்பட்டது');
    }
  };

  const validateForm = (): boolean => {
    if (!formData.name?.trim()) {
      Alert.alert('பிழை', 'பெயர் அவசியம்');
      return false;
    }
    if (!formData.gender) {
      Alert.alert('பிழை', 'பாலினம் தேர்ந்தெடுக்கவும்');
      return false;
    }
    if (!formData.rasi) {
      Alert.alert('பிழை', 'ராசி தேர்ந்தெடுக்கவும்');
      return false;
    }
    if (!formData.nathathiram) {
      Alert.alert('பிழை', 'நட்சத்திரம் தேர்ந்தெடுக்கவும்');
      return false;
    }
    if (!selectedDocument) {
      Alert.alert('பிழை', 'ஜாதக ஆவணம் தேர்ந்தெடுக்கவும்');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const documentData: JadhagamDocument = {
        ...formData as JadhagamDocument,
        document_path: selectedDocument?.uri || '',
        document_name: selectedDocument?.name || '',
        photo_path: selectedPhoto?.uri || '',
        photo_name: selectedPhoto?.name || '',
      };

      await databaseService.insertJadhagamDocument(documentData);

      Alert.alert(
        'வெற்றி',
        'ஜாதக ஆவணம் வெற்றிகரமாக பதிவேற்றப்பட்டது',
        [
          {
            text: 'சரி',
            onPress: () => {
              // Reset form
              setFormData({
                name: '',
                gender: '',
                rasi: '',
                nathathiram: '',
                jathi: '',
                city: '',
                birth_place: '',
                dosham: '',
              });
              setSelectedDocument(null);
              setSelectedPhoto(null);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error uploading document:', error);
      Alert.alert('பிழை', 'ஆவணம் பதிவேற்றுவதில் பிழை ஏற்பட்டது');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>தமிழ் ஜாதகம் மேலாண்மை</Text>
        <Text style={styles.headerSubtitle}>Tamil Jadhagam Manager</Text>
      </View>

      <View style={styles.form}>
        <Text style={styles.sectionTitle}>அடிப்படை தகவல்கள் - Basic Information</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>பெயர் - Name *</Text>
          <TextInput
            style={styles.input}
            value={formData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            placeholder="பெயரை உள்ளிடவும் - Enter name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>பாலினம் - Gender *</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.gender}
              onValueChange={(value) => handleInputChange('gender', value)}
              style={styles.picker}
            >
              <Picker.Item label="தேர்ந்தெடுக்கவும் - Select" value="" />
              <Picker.Item label="ஆண் - Male" value="ஆண் - Male" />
              <Picker.Item label="பெண் - Female" value="பெண் - Female" />
            </Picker>
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>ராசி - Rasi *</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.rasi}
              onValueChange={(value) => handleInputChange('rasi', value)}
              style={styles.picker}
            >
              <Picker.Item label="தேர்ந்தெடுக்கவும் - Select" value="" />
              {rasiList.map((rasi) => (
                <Picker.Item
                  key={rasi.id}
                  label={`${rasi.rasi_tamil} - ${rasi.rasi_english}`}
                  value={`${rasi.rasi_tamil} - ${rasi.rasi_english}`}
                />
              ))}
            </Picker>
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>நட்சத்திரம் - Nathathiram *</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.nathathiram}
              onValueChange={(value) => handleInputChange('nathathiram', value)}
              style={styles.picker}
            >
              <Picker.Item label="தேர்ந்தெடுக்கவும் - Select" value="" />
              {nathathiramList.map((nathathiram) => (
                <Picker.Item
                  key={nathathiram.id}
                  label={`${nathathiram.nathathiram_tamil} - ${nathathiram.nathathiram_english}`}
                  value={`${nathathiram.nathathiram_tamil} - ${nathathiram.nathathiram_english}`}
                />
              ))}
            </Picker>
          </View>
        </View>

        <Text style={styles.sectionTitle}>கூடுதல் தகவல்கள் - Additional Information</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>ஜாதி - Jathi/Caste</Text>
          <TextInput
            style={styles.input}
            value={formData.jathi}
            onChangeText={(value) => handleInputChange('jathi', value)}
            placeholder="ஜாதியை உள்ளிடவும் - Enter jathi"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>ஊர்/நகரம் - City/Town</Text>
          <TextInput
            style={styles.input}
            value={formData.city}
            onChangeText={(value) => handleInputChange('city', value)}
            placeholder="ஊரை உள்ளிடவும் - Enter city"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>பிறந்த இடம் - Birth Place</Text>
          <TextInput
            style={styles.input}
            value={formData.birth_place}
            onChangeText={(value) => handleInputChange('birth_place', value)}
            placeholder="பிறந்த இடத்தை உள்ளிடவும் - Enter birth place"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>தோஷம் - Dosham</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.dosham}
              onValueChange={(value) => handleInputChange('dosham', value)}
              style={styles.picker}
            >
              <Picker.Item label="தேர்ந்தெடுக்கவும் - Select" value="" />
              <Picker.Item label="தோஷம் இல்லை - No Dosham" value="தோஷம் இல்லை - No Dosham" />
              <Picker.Item label="செவ்வாய் தோஷம் - Chevvai Dosham" value="செவ்வாய் தோஷம் - Chevvai Dosham" />
              <Picker.Item label="நாக தோஷம் - Naga Dosham" value="நாக தோஷம் - Naga Dosham" />
              <Picker.Item label="சுத்த ஜாதகம் - Sutha Jadhagam" value="சுத்த ஜாதகம் - Sutha Jadhagam" />
            </Picker>
          </View>
        </View>

        <Text style={styles.sectionTitle}>ஆவணங்கள் - Documents</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>ஜாதக ஆவணம் - Jadhagam Document *</Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity style={styles.button} onPress={selectDocument}>
              <Text style={styles.buttonText}>தேர்ந்தெடுக்கவும்</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.scanButton]} onPress={scanDocument}>
              <Text style={styles.buttonText}>ஸ்கேன் செய்யவும்</Text>
            </TouchableOpacity>
          </View>
          {selectedDocument && (
            <Text style={styles.selectedFile}>✓ {selectedDocument.name}</Text>
          )}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>புகைப்படம் - Photo (Optional)</Text>
          <View style={styles.buttonRow}>
            <TouchableOpacity style={styles.button} onPress={selectPhoto}>
              <Text style={styles.buttonText}>தேர்ந்தெடுக்கவும்</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.cameraButton]} onPress={takePhoto}>
              <Text style={styles.buttonText}>எடுக்கவும்</Text>
            </TouchableOpacity>
          </View>
          {selectedPhoto && (
            <View style={styles.photoPreview}>
              <Image source={{ uri: selectedPhoto.uri }} style={styles.previewImage} />
              <Text style={styles.selectedFile}>✓ {selectedPhoto.name}</Text>
            </View>
          )}
        </View>

        <TouchableOpacity
          style={[styles.submitButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.submitButtonText}>பதிவேற்றம் - Upload</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  form: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 15,
    borderBottomWidth: 2,
    borderBottomColor: '#2196F3',
    paddingBottom: 5,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  picker: {
    height: 50,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 10,
  },
  button: {
    flex: 1,
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  scanButton: {
    backgroundColor: '#4CAF50',
  },
  cameraButton: {
    backgroundColor: '#FF9800',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  selectedFile: {
    marginTop: 8,
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '500',
  },
  photoPreview: {
    marginTop: 10,
    alignItems: 'center',
  },
  previewImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginBottom: 5,
  },
  submitButton: {
    backgroundColor: '#4CAF50',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  reactLogo: {
    height: 178,
    width: 290,
    bottom: 0,
    left: 0,
    position: 'absolute',
  },
});
