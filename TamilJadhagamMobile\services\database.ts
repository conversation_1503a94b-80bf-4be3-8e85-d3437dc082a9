import * as SQLite from 'expo-sqlite';

export interface JadhagamDocument {
  id?: number;
  name: string;
  gender: string;
  rasi: string;
  nathathiram: string;
  jathi?: string;
  city?: string;
  birth_place?: string;
  dosham?: string;
  document_path?: string;
  document_name?: string;
  photo_path?: string;
  photo_name?: string;
  upload_date?: string;
}

export interface RasiItem {
  id: number;
  rasi_tamil: string;
  rasi_english: string;
}

export interface NathathiramItem {
  id: number;
  nathathiram_tamil: string;
  nathathiram_english: string;
}

class DatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;

  async initializeDatabase(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync('tamil_jadhagam.db');
      await this.createTables();
      await this.insertDefaultData();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const createJadhagamTable = `
      CREATE TABLE IF NOT EXISTS jadhagam_documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        gender TEXT NOT NULL,
        rasi TEXT NOT NULL,
        nathathiram TEXT NOT NULL,
        jathi TEXT,
        city TEXT,
        birth_place TEXT,
        dosham TEXT,
        document_path TEXT,
        document_name TEXT,
        photo_path TEXT,
        photo_name TEXT,
        upload_date DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `;

    const createRasiTable = `
      CREATE TABLE IF NOT EXISTS rasi_list (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        rasi_tamil TEXT NOT NULL,
        rasi_english TEXT NOT NULL
      );
    `;

    const createNathathiramTable = `
      CREATE TABLE IF NOT EXISTS nathathiram_list (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nathathiram_tamil TEXT NOT NULL,
        nathathiram_english TEXT NOT NULL
      );
    `;

    await this.db.execAsync(createJadhagamTable);
    await this.db.execAsync(createRasiTable);
    await this.db.execAsync(createNathathiramTable);
  }

  private async insertDefaultData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Check if data already exists
    const rasiCount = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM rasi_list');
    if ((rasiCount as any)?.count > 0) return;

    // Insert Rasi data
    const rasiData = [
      ['மேஷம்', 'Mesham'],
      ['ரிஷபம்', 'Rishabam'],
      ['மிதுனம்', 'Mithunam'],
      ['கடகம்', 'Kadagam'],
      ['சிம்மம்', 'Simmam'],
      ['கன்னி', 'Kanni'],
      ['துலாம்', 'Thulam'],
      ['விருச்சிகம்', 'Viruchigam'],
      ['தனுசு', 'Dhanusu'],
      ['மகரம்', 'Magaram'],
      ['கும்பம்', 'Kumbam'],
      ['மீனம்', 'Meenam']
    ];

    for (const [tamil, english] of rasiData) {
      await this.db.runAsync(
        'INSERT INTO rasi_list (rasi_tamil, rasi_english) VALUES (?, ?)',
        [tamil, english]
      );
    }

    // Insert Nathathiram data
    const nathathiramData = [
      ['அசுவினி', 'Aswini'],
      ['பரணி', 'Bharani'],
      ['கிருத்திகை', 'Krittika'],
      ['ரோகிணி', 'Rohini'],
      ['மிருகசீரிடம்', 'Mrigashirsha'],
      ['திருவாதிரை', 'Ardra'],
      ['புனர்பூசம்', 'Punarvasu'],
      ['பூசம்', 'Pushya'],
      ['ஆயில்யம்', 'Ashlesha'],
      ['மகம்', 'Magha'],
      ['பூரம்', 'Purva Phalguni'],
      ['உத்திரம்', 'Uttara Phalguni'],
      ['ஹஸ்தம்', 'Hasta'],
      ['சித்திரை', 'Chitra'],
      ['சுவாதி', 'Swati'],
      ['விசாகம்', 'Vishakha'],
      ['அனுஷம்', 'Anuradha'],
      ['கேட்டை', 'Jyeshtha'],
      ['மூலம்', 'Mula'],
      ['பூராடம்', 'Purva Ashadha'],
      ['உத்திராடம்', 'Uttara Ashadha'],
      ['திருவோணம்', 'Shravana'],
      ['அவிட்டம்', 'Dhanishta'],
      ['சதயம்', 'Shatabhisha'],
      ['பூரட்டாதி', 'Purva Bhadrapada'],
      ['உத்திரட்டாதி', 'Uttara Bhadrapada'],
      ['ரேவதி', 'Revati']
    ];

    for (const [tamil, english] of nathathiramData) {
      await this.db.runAsync(
        'INSERT INTO nathathiram_list (nathathiram_tamil, nathathiram_english) VALUES (?, ?)',
        [tamil, english]
      );
    }
  }

  async getRasiList(): Promise<RasiItem[]> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.getAllAsync('SELECT * FROM rasi_list ORDER BY id') as RasiItem[];
  }

  async getNathathiramList(): Promise<NathathiramItem[]> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.getAllAsync('SELECT * FROM nathathiram_list ORDER BY id') as NathathiramItem[];
  }

  async insertJadhagamDocument(document: JadhagamDocument): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.runAsync(
      `INSERT INTO jadhagam_documents 
       (name, gender, rasi, nathathiram, jathi, city, birth_place, dosham, document_path, document_name, photo_path, photo_name) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        document.name,
        document.gender,
        document.rasi,
        document.nathathiram,
        document.jathi || '',
        document.city || '',
        document.birth_place || '',
        document.dosham || '',
        document.document_path || '',
        document.document_name || '',
        document.photo_path || '',
        document.photo_name || ''
      ]
    );

    return result.lastInsertRowId;
  }

  async searchJadhagamDocuments(searchParams: Partial<JadhagamDocument>): Promise<JadhagamDocument[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = 'SELECT * FROM jadhagam_documents WHERE 1=1';
    const params: any[] = [];

    if (searchParams.name) {
      query += ' AND name LIKE ?';
      params.push(`%${searchParams.name}%`);
    }
    if (searchParams.gender) {
      query += ' AND gender LIKE ?';
      params.push(`%${searchParams.gender}%`);
    }
    if (searchParams.rasi) {
      query += ' AND rasi LIKE ?';
      params.push(`%${searchParams.rasi}%`);
    }
    if (searchParams.nathathiram) {
      query += ' AND nathathiram LIKE ?';
      params.push(`%${searchParams.nathathiram}%`);
    }
    if (searchParams.jathi) {
      query += ' AND jathi LIKE ?';
      params.push(`%${searchParams.jathi}%`);
    }
    if (searchParams.city) {
      query += ' AND city LIKE ?';
      params.push(`%${searchParams.city}%`);
    }
    if (searchParams.dosham) {
      query += ' AND dosham LIKE ?';
      params.push(`%${searchParams.dosham}%`);
    }

    query += ' ORDER BY upload_date DESC';

    return await this.db.getAllAsync(query, params) as JadhagamDocument[];
  }

  async getAllJadhagamDocuments(): Promise<JadhagamDocument[]> {
    if (!this.db) throw new Error('Database not initialized');
    return await this.db.getAllAsync('SELECT * FROM jadhagam_documents ORDER BY upload_date DESC') as JadhagamDocument[];
  }

  async deleteJadhagamDocument(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.runAsync('DELETE FROM jadhagam_documents WHERE id = ?', [id]);
  }

  async updateJadhagamDocument(id: number, document: Partial<JadhagamDocument>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const fields = Object.keys(document).filter(key => key !== 'id');
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => (document as any)[field]);

    await this.db.runAsync(
      `UPDATE jadhagam_documents SET ${setClause} WHERE id = ?`,
      [...values, id]
    );
  }
}

export const databaseService = new DatabaseService();
