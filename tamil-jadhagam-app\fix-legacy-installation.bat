@echo off
REM Tamil Jadhagam Manager - Legacy System Installation Fix
REM தமிழ் ஜாதகம் மேலாண்மை - பழைய அமைப்பு நிறுவல் சரிசெய்தல்

echo ========================================
echo Tamil Jadhagam Manager - Legacy Fix
echo தமிழ் ஜாதகம் மேலாண்மை - பழைய அமைப்பு சரிசெய்தல்
echo ========================================
echo.
echo This script will help fix installation issues on older systems
echo இந்த ஸ்கிரிப்ட் பழைய அமைப்புகளில் நிறுவல் சிக்கல்களை சரிசெய்ய உதவும்
echo.
echo Detected system information:
echo கண்டறியப்பட்ட அமைப்பு தகவல்:
echo.

REM Display system information
echo OS Version: 
ver
echo.

echo Architecture:
wmic os get osarchitecture /value | find "OSArchitecture"
echo.

echo Processor:
wmic cpu get name /value | find "Name"
echo.

echo Total RAM:
wmic computersystem get TotalPhysicalMemory /value | find "TotalPhysicalMemory"
echo.

echo ========================================
echo LEGACY SYSTEM COMPATIBILITY FIXES
echo பழைய அமைப்பு இணக்கத்தன்மை சரிசெய்தல்கள்
echo ========================================
echo.

echo [1/5] Checking Visual C++ Redistributables...
echo Visual C++ Redistributables ஐ சரிபார்க்கிறது...
echo.

REM Check for Visual C++ Redistributables
reg query "HKLM\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x86" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Visual C++ 2015-2022 (x86) found
    echo ✓ Visual C++ 2015-2022 (x86) கண்டறியப்பட்டது
) else (
    echo ✗ Visual C++ 2015-2022 (x86) NOT found
    echo ✗ Visual C++ 2015-2022 (x86) கண்டறியப்படவில்லை
    echo.
    echo SOLUTION: Download and install from:
    echo தீர்வு: இங்கிருந்து பதிவிறக்கம் செய்து நிறுவவும்:
    echo https://aka.ms/vs/17/release/vc_redist.x86.exe
    echo.
)

echo [2/5] Checking .NET Framework...
echo .NET Framework ஐ சரிபார்க்கிறது...
echo.

REM Check for .NET Framework
reg query "HKLM\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ .NET Framework 4.x found
    echo ✓ .NET Framework 4.x கண்டறியப்பட்டது
) else (
    echo ✗ .NET Framework 4.x NOT found
    echo ✗ .NET Framework 4.x கண்டறியப்படவில்லை
    echo.
    echo SOLUTION: Install .NET Framework 4.7.2 or later
    echo தீர்வு: .NET Framework 4.7.2 அல்லது அதற்கு பிந்தைய பதிப்பை நிறுவவும்
    echo.
)

echo [3/5] Checking Windows Updates...
echo Windows Updates ஐ சரிபார்க்கிறது...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
if "%VERSION%" == "6.1" (
    echo ✓ Windows 7 detected
    echo ✓ Windows 7 கண்டறியப்பட்டது
    echo.
    echo RECOMMENDATION: Ensure Service Pack 1 is installed
    echo பரிந்துரை: Service Pack 1 நிறுவப்பட்டுள்ளதா என்பதை உறுதிப்படுத்தவும்
) else (
    echo ✓ Windows version: %VERSION%
    echo ✓ Windows பதிப்பு: %VERSION%
)

echo.
echo [4/5] Checking available disk space...
echo கிடைக்கும் வட்டு இடத்தை சரிபார்க்கிறது...
echo.

REM Check disk space
for /f "tokens=3" %%a in ('dir C:\ /-c ^| find "bytes free"') do set FREESPACE=%%a
echo Available space on C: drive: %FREESPACE% bytes
echo C: இயக்ககத்தில் கிடைக்கும் இடம்: %FREESPACE% bytes

echo.
echo [5/5] Creating compatibility installer...
echo இணக்கத்தன்மை நிறுவியை உருவாக்குகிறது...
echo.

REM Create compatibility batch file for installation
echo @echo off > install_with_compatibility.bat
echo echo Installing Tamil Jadhagam Manager with compatibility settings... >> install_with_compatibility.bat
echo echo தமிழ் ஜாதகம் மேலாண்மையை இணக்கத்தன்மை அமைப்புகளுடன் நிறுவுகிறது... >> install_with_compatibility.bat
echo. >> install_with_compatibility.bat
echo REM Run installer with compatibility flags >> install_with_compatibility.bat
echo if exist "Tamil-Jadhagam-Manager-Setup.exe" ( >> install_with_compatibility.bat
echo     echo Running installer with Windows 7 compatibility... >> install_with_compatibility.bat
echo     echo Windows 7 இணக்கத்தன்மையுடன் நிறுவியை இயக்குகிறது... >> install_with_compatibility.bat
echo     Tamil-Jadhagam-Manager-Setup.exe /NCRC >> install_with_compatibility.bat
echo ^) else ( >> install_with_compatibility.bat
echo     echo ERROR: Tamil-Jadhagam-Manager-Setup.exe not found in current directory >> install_with_compatibility.bat
echo     echo பிழை: தற்போதைய கோப்பகத்தில் Tamil-Jadhagam-Manager-Setup.exe கண்டறியப்படவில்லை >> install_with_compatibility.bat
echo     echo Please copy the installer to this folder and run again >> install_with_compatibility.bat
echo     echo தயவுசெய்து நிறுவியை இந்த கோப்பகத்திற்கு நகலெடுத்து மீண்டும் இயக்கவும் >> install_with_compatibility.bat
echo ^) >> install_with_compatibility.bat
echo pause >> install_with_compatibility.bat

echo ✓ Compatibility installer created: install_with_compatibility.bat
echo ✓ இணக்கத்தன்மை நிறுவி உருவாக்கப்பட்டது: install_with_compatibility.bat

echo.
echo ========================================
echo INSTALLATION INSTRUCTIONS
echo நிறுவல் வழிமுறைகள்
echo ========================================
echo.

echo STEP 1: Install Required Components
echo படி 1: தேவையான கூறுகளை நிறுவவும்
echo.
echo 1. Download and install Visual C++ Redistributable (x86):
echo    Visual C++ Redistributable (x86) ஐ பதிவிறக்கம் செய்து நிறுவவும்:
echo    https://aka.ms/vs/17/release/vc_redist.x86.exe
echo.
echo 2. Install all Windows Updates
echo    அனைத்து Windows Updates ஐ நிறுவவும்
echo.
echo 3. Restart your computer
echo    உங்கள் கணினியை மறுதொடக்கம் செய்யவும்
echo.

echo STEP 2: Install Tamil Jadhagam Manager
echo படி 2: தமிழ் ஜாதகம் மேலாண்மையை நிறுவவும்
echo.
echo Option A: Use compatibility installer
echo விருப்பம் A: இணக்கத்தன்மை நிறுவியைப் பயன்படுத்தவும்
echo.
echo 1. Copy Tamil-Jadhagam-Manager-Setup.exe to this folder
echo    Tamil-Jadhagam-Manager-Setup.exe ஐ இந்த கோப்பகத்திற்கு நகலெடுக்கவும்
echo.
echo 2. Run: install_with_compatibility.bat
echo    இயக்கவும்: install_with_compatibility.bat
echo.

echo Option B: Manual compatibility installation
echo விருப்பம் B: கைமுறை இணக்கத்தன்மை நிறுவல்
echo.
echo 1. Right-click on Tamil-Jadhagam-Manager-Setup.exe
echo    Tamil-Jadhagam-Manager-Setup.exe இல் வலது கிளிக் செய்யவும்
echo.
echo 2. Select Properties → Compatibility tab
echo    Properties → Compatibility tab ஐ தேர்ந்தெடுக்கவும்
echo.
echo 3. Check "Run this program in compatibility mode for Windows 7"
echo    "Run this program in compatibility mode for Windows 7" ஐ தேர்வு செய்யவும்
echo.
echo 4. Check "Run as administrator"
echo    "Run as administrator" ஐ தேர்வு செய்யவும்
echo.
echo 5. Click OK and run the installer
echo    OK கிளிக் செய்து நிறுவியை இயக்கவும்
echo.

echo ========================================
echo TROUBLESHOOTING TIPS
echo சிக்கல் தீர்க்கும் குறிப்புகள்
echo ========================================
echo.
echo • If installation still fails, try installing in Safe Mode
echo • நிறுவல் இன்னும் தோல்வியுற்றால், Safe Mode இல் நிறுவ முயற்சிக்கவும்
echo.
echo • Temporarily disable antivirus during installation
echo • நிறுவலின் போது antivirus ஐ தற்காலிகமாக முடக்கவும்
echo.
echo • Ensure at least 1GB free space on C: drive
echo • C: இயக்ககத்தில் குறைந்தது 1GB இடம் இருப்பதை உறுதிப்படுத்தவும்
echo.
echo • Close all other programs before installation
echo • நிறுவலுக்கு முன் மற்ற எல்லா நிரல்களையும் மூடவும்
echo.

echo ========================================
echo Tamil Jadhagam Manager v1.3.3
echo தமிழ் ஜாதகம் மேலாண்மை v1.3.3
echo Legacy System Support Ready!
echo பழைய அமைப்பு ஆதரவு தயார்!
echo ========================================
echo.
echo For additional support, refer to: LEGACY_SYSTEM_SUPPORT.md
echo கூடுதல் ஆதரவுக்கு, இதைப் பார்க்கவும்: LEGACY_SYSTEM_SUPPORT.md
echo.
pause
