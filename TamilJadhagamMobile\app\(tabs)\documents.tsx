import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Alert,
  Image,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { databaseService, JadhagamDocument } from '@/services/database';
import { fileService } from '@/services/fileService';

export default function DocumentsScreen() {
  const [documents, setDocuments] = useState<JadhagamDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    useCallback(() => {
      loadDocuments();
    }, [])
  );

  const loadDocuments = async () => {
    try {
      const allDocuments = await databaseService.getAllJadhagamDocuments();
      setDocuments(allDocuments);
    } catch (error) {
      console.error('Error loading documents:', error);
      Alert.alert('பிழை', 'ஆவணங்களை ஏற்றுவதில் பிழை ஏற்பட்டது');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDocuments();
    setRefreshing(false);
  };

  const handleDocumentAction = async (document: JadhagamDocument, action: 'view' | 'share' | 'delete') => {
    switch (action) {
      case 'view':
        Alert.alert(
          'ஆவண விவரங்கள் - Document Details',
          `பெயர்: ${document.name}\nபாலினம்: ${document.gender}\nராசி: ${document.rasi}\nநட்சத்திரம்: ${document.nathathiram}\nஜாதி: ${document.jathi || 'N/A'}\nஊர்: ${document.city || 'N/A'}\nபிறந்த இடம்: ${document.birth_place || 'N/A'}\nதோஷம்: ${document.dosham || 'N/A'}\nபதிவேற்ற தேதி: ${document.upload_date}`,
          [
            { text: 'சரி', style: 'default' },
            {
              text: 'ஆவணத்தைப் பார்க்க',
              onPress: () => {
                if (document.document_path) {
                  // In a real app, you would open the document with a document viewer
                  Alert.alert('தகவல்', 'ஆவணம்: ' + document.document_name);
                }
              },
            },
          ]
        );
        break;
      
      case 'share':
        if (document.document_path) {
          const success = await fileService.shareFile(document.document_path, document.document_name || 'document');
          if (success) {
            Alert.alert('வெற்றி', 'ஆவணம் பகிரப்பட்டது');
          } else {
            Alert.alert('பிழை', 'ஆவணம் பகிர முடியவில்லை');
          }
        } else {
          Alert.alert('பிழை', 'பகிர ஆவணம் இல்லை');
        }
        break;
      
      case 'delete':
        Alert.alert(
          'உறுதிப்படுத்தல்',
          'இந்த ஆவணத்தை நிச்சயமாக நீக்க விரும்புகிறீர்களா?',
          [
            { text: 'ரத்து', style: 'cancel' },
            {
              text: 'நீக்கு',
              style: 'destructive',
              onPress: async () => {
                try {
                  if (document.id) {
                    // Delete files
                    if (document.document_path) {
                      await fileService.deleteFile(document.document_path);
                    }
                    if (document.photo_path) {
                      await fileService.deleteFile(document.photo_path);
                    }
                    
                    // Delete from database
                    await databaseService.deleteJadhagamDocument(document.id);
                    
                    // Remove from local state
                    setDocuments(prev => prev.filter(item => item.id !== document.id));
                    Alert.alert('வெற்றி', 'ஆவணம் நீக்கப்பட்டது');
                  }
                } catch (error) {
                  console.error('Error deleting document:', error);
                  Alert.alert('பிழை', 'ஆவணம் நீக்குவதில் பிழை ஏற்பட்டது');
                }
              },
            },
          ]
        );
        break;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('ta-IN');
    } catch {
      return dateString;
    }
  };

  const renderDocument = ({ item }: { item: JadhagamDocument }) => (
    <View style={styles.documentCard}>
      <View style={styles.documentHeader}>
        <View style={styles.documentInfo}>
          <Text style={styles.documentName}>{item.name}</Text>
          <Text style={styles.documentDetails}>
            {item.gender} • {item.rasi} • {item.nathathiram}
          </Text>
          {item.jathi && <Text style={styles.documentSubDetails}>ஜாதி: {item.jathi}</Text>}
          {item.city && <Text style={styles.documentSubDetails}>ஊர்: {item.city}</Text>}
          {item.birth_place && <Text style={styles.documentSubDetails}>பிறந்த இடம்: {item.birth_place}</Text>}
          {item.dosham && <Text style={styles.documentSubDetails}>தோஷம்: {item.dosham}</Text>}
          <Text style={styles.documentDate}>பதிவேற்றம்: {formatDate(item.upload_date)}</Text>
        </View>
        {item.photo_path && (
          <TouchableOpacity onPress={() => {
            Alert.alert('புகைப்படம்', '', [
              { text: 'சரி', style: 'default' }
            ]);
          }}>
            <Image source={{ uri: item.photo_path }} style={styles.documentPhoto} />
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.documentActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.viewButton]}
          onPress={() => handleDocumentAction(item, 'view')}
        >
          <Text style={styles.actionButtonText}>பார்க்க</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.shareButton]}
          onPress={() => handleDocumentAction(item, 'share')}
        >
          <Text style={styles.actionButtonText}>பகிர்</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDocumentAction(item, 'delete')}
        >
          <Text style={styles.actionButtonText}>நீக்கு</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateTitle}>ஆவணங்கள் இல்லை</Text>
      <Text style={styles.emptyStateSubtitle}>
        புதிய ஜாதக ஆவணங்களைப் பதிவேற்ற "பதிவேற்றம்" தாவலைப் பயன்படுத்தவும்
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF9800" />
        <Text style={styles.loadingText}>ஆவணங்கள் ஏற்றப்படுகின்றன...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>ஆவணங்கள் - Documents</Text>
        <Text style={styles.headerSubtitle}>
          மொத்தம் {documents.length} ஆவணங்கள்
        </Text>
      </View>

      <FlatList
        data={documents}
        renderItem={renderDocument}
        keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
        style={styles.documentsList}
        contentContainerStyle={documents.length === 0 ? styles.emptyContainer : undefined}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#FF9800']}
            tintColor="#FF9800"
          />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#FF9800',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  documentsList: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 10,
  },
  emptyStateSubtitle: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
    lineHeight: 24,
  },
  documentCard: {
    backgroundColor: '#fff',
    margin: 10,
    padding: 15,
    borderRadius: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  documentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  documentDetails: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  documentSubDetails: {
    fontSize: 12,
    color: '#888',
    marginBottom: 2,
  },
  documentDate: {
    fontSize: 11,
    color: '#999',
    marginTop: 5,
    fontStyle: 'italic',
  },
  documentPhoto: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginLeft: 10,
  },
  documentActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  viewButton: {
    backgroundColor: '#2196F3',
  },
  shareButton: {
    backgroundColor: '#FF9800',
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});
