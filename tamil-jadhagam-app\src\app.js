// Tamil Jadhagam Management App - Frontend JavaScript

class JadhagamApp {
    constructor() {
        this.currentTab = 'upload';
        this.rasiList = [];
        this.nathathiramList = [];
        this.jadthiList = [];
        this.cityList = [];
        this.searchResults = [];
        this.selectedDocument = null;
        this.selectedPhoto = null;

        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadDropdownData();
        this.showTab('upload');
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('data-tab');
                this.showTab(tabName);
            });
        });

        // Upload form
        document.getElementById('upload-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleUpload();
        });

        // Search form
        document.getElementById('search-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSearch();
        });

        // Clear search
        document.getElementById('clear-search').addEventListener('click', () => {
            this.clearSearch();
        });

        // File selection buttons
        document.getElementById('select-document-btn').addEventListener('click', () => {
            this.selectDocumentFile();
        });

        document.getElementById('select-photo-btn').addEventListener('click', () => {
            this.selectPhotoFile();
        });
    }

    showTab(tabName) {
        // Update tab buttons with animation
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        const activeButton = document.querySelector(`[data-tab="${tabName}"]`);
        activeButton.classList.add('active');

        // Update tab content with fade animation
        document.querySelectorAll('.tab-content').forEach(content => {
            if (content.classList.contains('active')) {
                content.style.opacity = '0';
                content.style.transform = 'translateY(10px)';
                setTimeout(() => {
                    content.classList.remove('active');
                }, 150);
            }
        });

        setTimeout(() => {
            const newContent = document.getElementById(`${tabName}-tab`);
            newContent.classList.add('active');
            newContent.style.opacity = '0';
            newContent.style.transform = 'translateY(10px)';

            // Trigger reflow
            newContent.offsetHeight;

            newContent.style.transition = 'all 0.3s ease-out';
            newContent.style.opacity = '1';
            newContent.style.transform = 'translateY(0)';
        }, 150);

        this.currentTab = tabName;
    }

    async loadDropdownData() {
        try {
            this.showLoading(true);

            // Load all dropdown data
            const [rasiList, nathathiramList, jadthiList, cityList] = await Promise.all([
                window.electronAPI.getRasiList(),
                window.electronAPI.getNathathiramList(),
                window.electronAPI.getJadthiList(),
                window.electronAPI.getCityList()
            ]);

            this.rasiList = rasiList;
            this.nathathiramList = nathathiramList;
            this.jadthiList = jadthiList;
            this.cityList = cityList;

            this.populateDropdowns();
        } catch (error) {
            console.error('Error loading dropdown data:', error);
            this.showMessage('Error loading data: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    populateDropdowns() {
        // Populate Rasi dropdowns
        this.populateSelect('rasi', this.rasiList, 'rasi_tamil');
        this.populateSelect('search-rasi', this.rasiList, 'rasi_tamil');

        // Populate Nathathiram dropdowns
        this.populateSelect('nathathiram', this.nathathiramList, 'nathathiram_tamil');
        this.populateSelect('search-nathathiram', this.nathathiramList, 'nathathiram_tamil');

        // Populate Jadthi dropdowns
        this.populateSelect('jadthi', this.jadthiList, 'jadthi_tamil');
        this.populateSelect('search-jadthi', this.jadthiList, 'jadthi_tamil');

        // Populate City dropdowns
        this.populateSelect('city', this.cityList, 'city_tamil');
        this.populateSelect('search-city', this.cityList, 'city_tamil');
    }

    populateSelect(selectId, data, valueField) {
        const select = document.getElementById(selectId);
        if (!select) return;

        // Keep the first option (placeholder)
        const firstOption = select.querySelector('option');
        select.innerHTML = '';
        if (firstOption) {
            select.appendChild(firstOption);
        }

        // Add data options
        data.forEach(item => {
            const option = document.createElement('option');
            option.value = item[valueField];
            option.textContent = item[valueField];
            select.appendChild(option);
        });
    }

    async handleUpload() {
        try {
            this.showLoading(true);

            // Get form data
            const form = document.getElementById('upload-form');
            const formData = new FormData(form);
            const jadhagamData = {};

            // Extract text fields
            for (let [key, value] of formData.entries()) {
                if (!(value instanceof File)) {
                    jadhagamData[key] = value;
                }
            }

            // Validate required fields
            const requiredFields = ['name', 'rasi', 'nathathiram'];
            for (let field of requiredFields) {
                if (!jadhagamData[field]) {
                    throw new Error(`${field} is required - ${field} தேவை`);
                }
            }

            // Handle file uploads
            const documentFile = formData.get('document');
            const photoFile = formData.get('photo');

            if (!documentFile || documentFile.size === 0) {
                throw new Error('Document file is required - ஆவணம் தேவை');
            }

            // Add file information to jadhagamData
            if (documentFile && documentFile.size > 0) {
                jadhagamData.document = {
                    name: documentFile.name,
                    path: documentFile.path || documentFile.webkitRelativePath,
                    size: documentFile.size,
                    type: documentFile.type
                };
            }

            if (photoFile && photoFile.size > 0) {
                jadhagamData.photo = {
                    name: photoFile.name,
                    path: photoFile.path || photoFile.webkitRelativePath,
                    size: photoFile.size,
                    type: photoFile.type
                };
            }

            // Upload jadhagam
            const result = await window.electronAPI.uploadJadhagam(jadhagamData);

            if (result.success) {
                this.showMessage(result.message, 'success');

                // Add success animation to form
                form.style.transform = 'scale(0.98)';
                form.style.opacity = '0.7';

                setTimeout(() => {
                    form.reset();
                    // Hide photo preview
                    document.getElementById('photo-preview').style.display = 'none';
                    form.style.transition = 'all 0.3s ease-out';
                    form.style.transform = 'scale(1)';
                    form.style.opacity = '1';
                }, 200);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showMessage('Upload failed: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async selectDocumentFile() {
        try {
            const result = await window.electronAPI.selectDocumentFile();
            if (result.success) {
                this.selectedDocument = result.file;
                this.displaySelectedFile('document', result.file);
            } else {
                console.log('Document selection cancelled');
            }
        } catch (error) {
            console.error('Error selecting document:', error);
            this.showMessage('Failed to select document: ' + error.message, 'error');
        }
    }

    async selectPhotoFile() {
        try {
            const result = await window.electronAPI.selectPhotoFile();
            if (result.success) {
                this.selectedPhoto = result.file;
                this.displaySelectedFile('photo', result.file);
                this.showPhotoPreview(result.file.path);
            } else {
                console.log('Photo selection cancelled');
            }
        } catch (error) {
            console.error('Error selecting photo:', error);
            this.showMessage('Failed to select photo: ' + error.message, 'error');
        }
    }

    displaySelectedFile(type, file) {
        const container = document.getElementById(`selected-${type}`);
        const fileName = container.querySelector('.file-name');
        const fileSize = container.querySelector('.file-size');

        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        container.style.display = 'flex';
        container.style.animation = 'fadeIn 0.3s ease-out';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showPhotoPreview(photoPath) {
        const previewContainer = document.getElementById('photo-preview');
        const previewImage = document.getElementById('preview-image');

        if (photoPath) {
            previewImage.src = `file://${photoPath}`;
            previewContainer.style.display = 'block';
            previewContainer.style.animation = 'fadeIn 0.5s ease-out';
        } else {
            previewContainer.style.display = 'none';
        }
    }

    async handleSearch() {
        try {
            this.showLoading(true);

            // Get search parameters
            const formData = new FormData(document.getElementById('search-form'));
            const searchParams = {};
            
            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    searchParams[key] = value.trim();
                }
            }

            // Perform search
            const result = await window.electronAPI.searchJadhagam(searchParams);
            
            if (result.success) {
                this.searchResults = result.data;
                this.displaySearchResults();
                this.showTab('results');
                this.showMessage(`${result.data.length} முடிவுகள் கண்டறியப்பட்டன - ${result.data.length} results found`, 'info');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showMessage('Search failed: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    displaySearchResults() {
        const resultsContainer = document.getElementById('results-list');
        
        if (this.searchResults.length === 0) {
            resultsContainer.innerHTML = '<p class="no-results">தேடல் முடிவுகள் இல்லை - No search results</p>';
            return;
        }

        const resultsHTML = this.searchResults.map(result => {
            const uploadDate = new Date(result.upload_date).toLocaleDateString('ta-IN');
            const photoHTML = result.photo_path ?
                `<img src="file://${result.photo_path}" alt="Photo" class="result-photo" onclick="app.showPhotoModal('${result.photo_path.replace(/\\/g, '\\\\')}')" title="Click to enlarge">` : '';

            return `
                <div class="result-item">
                    <div class="result-header">
                        <div class="result-name">${result.name}</div>
                        <div class="result-date">${uploadDate}</div>
                    </div>
                    <div class="result-content">
                        ${photoHTML}
                        <div class="result-details">
                            <div class="result-detail">
                                <div class="result-detail-label">ராசி - Rasi</div>
                                <div class="result-detail-value">${result.rasi}</div>
                            </div>
                            <div class="result-detail">
                                <div class="result-detail-label">நட்சத்திரம் - Nathathiram</div>
                                <div class="result-detail-value">${result.nathathiram}</div>
                            </div>
                            ${result.jadthi ? `
                            <div class="result-detail">
                                <div class="result-detail-label">ஜாதி - Jathi</div>
                                <div class="result-detail-value">${result.jadthi}</div>
                            </div>
                            ` : ''}
                            ${result.city ? `
                            <div class="result-detail">
                                <div class="result-detail-label">ஊர்/நகரம் - City</div>
                                <div class="result-detail-value">${result.city}</div>
                            </div>
                            ` : ''}
                            ${result.dosham ? `
                            <div class="result-detail">
                                <div class="result-detail-label">தோஷம் - Dosham</div>
                                <div class="result-detail-value">${result.dosham}</div>
                            </div>
                            ` : ''}
                        ${result.birth_date ? `
                        <div class="result-detail">
                            <div class="result-detail-label">பிறந்த தேதி - Birth Date</div>
                            <div class="result-detail-value">${new Date(result.birth_date).toLocaleDateString('ta-IN')}</div>
                        </div>
                        ` : ''}
                        ${result.gender ? `
                        <div class="result-detail">
                            <div class="result-detail-label">பாலினம் - Gender</div>
                            <div class="result-detail-value">${result.gender}</div>
                        </div>
                        ` : ''}
                            ${result.notes ? `
                            <div class="result-detail">
                                <div class="result-detail-label">குறிப்புகள் - Notes</div>
                                <div class="result-detail-value">${result.notes}</div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="result-actions">
                        <button class="btn-open" onclick="app.openDocument('${result.file_path.replace(/\\/g, '\\\\')}')">
                            <i class="fas fa-external-link-alt"></i>
                            ஆவணத்தைத் திற - Open Document
                        </button>
                        <button class="btn-download" onclick="app.downloadDocument('${result.file_path.replace(/\\/g, '\\\\')}', '${result.file_name}')">
                            <i class="fas fa-download"></i>
                            பதிவிறக்கம் - Download
                        </button>
                        <button class="btn-delete" onclick="app.deleteDocument(${result.id}, '${result.file_path.replace(/\\/g, '\\\\')}')">
                            <i class="fas fa-trash-alt"></i>
                            நீக்கு - Delete
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        resultsContainer.innerHTML = resultsHTML;
    }

    async openDocument(filePath) {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.openDocument(filePath);
            if (!result.success) {
                throw new Error(result.message);
            }
            this.showMessage('ஆவணம் திறக்கப்பட்டது - Document opened', 'success');
        } catch (error) {
            console.error('Error opening document:', error);
            this.showMessage('Failed to open document: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async downloadDocument(filePath, originalFileName) {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.downloadDocument(filePath, originalFileName);
            if (!result.success) {
                throw new Error(result.message);
            }
            this.showMessage(result.message, 'success');
        } catch (error) {
            console.error('Error downloading document:', error);
            this.showMessage('Download failed: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async deleteDocument(documentId, filePath) {
        try {
            this.showLoading(true);
            const result = await window.electronAPI.deleteDocument(documentId, filePath);
            if (!result.success) {
                throw new Error(result.message);
            }
            this.showMessage(result.message, 'success');

            // Refresh search results to remove deleted item
            if (this.searchResults.length > 0) {
                // Remove the deleted item from current results
                this.searchResults = this.searchResults.filter(item => item.id !== documentId);
                this.displaySearchResults();
            }
        } catch (error) {
            console.error('Error deleting document:', error);
            this.showMessage('Delete failed: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    clearSearch() {
        document.getElementById('search-form').reset();
        this.searchResults = [];
        document.getElementById('results-list').innerHTML = '<p class="no-results">தேடல் முடிவுகள் இல்லை - No search results</p>';
    }

    showPhotoModal(photoPath) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('photo-modal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'photo-modal';
            modal.className = 'photo-modal';
            modal.innerHTML = `
                <div class="photo-modal-close" onclick="app.closePhotoModal()">&times;</div>
                <img src="" alt="Full Size Photo">
            `;
            document.body.appendChild(modal);

            // Close modal when clicking outside the image
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closePhotoModal();
                }
            });

            // Close modal with Escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && modal.style.display === 'flex') {
                    this.closePhotoModal();
                }
            });
        }

        // Set image source and show modal
        const img = modal.querySelector('img');
        img.src = `file://${photoPath}`;
        modal.style.display = 'flex';
        modal.style.opacity = '0';

        // Trigger reflow
        modal.offsetHeight;

        modal.style.transition = 'opacity 0.3s ease-out';
        modal.style.opacity = '1';
    }

    closePhotoModal() {
        const modal = document.getElementById('photo-modal');
        if (modal) {
            modal.style.transition = 'opacity 0.3s ease-out';
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        if (show) {
            loading.classList.remove('hidden');
            loading.style.opacity = '0';
            loading.style.transform = 'scale(0.9)';

            // Trigger reflow
            loading.offsetHeight;

            loading.style.transition = 'all 0.3s ease-out';
            loading.style.opacity = '1';
            loading.style.transform = 'scale(1)';
        } else {
            loading.style.transition = 'all 0.3s ease-out';
            loading.style.opacity = '0';
            loading.style.transform = 'scale(0.9)';

            setTimeout(() => {
                loading.classList.add('hidden');
            }, 300);
        }
    }

    showMessage(message, type = 'info') {
        const messageEl = document.getElementById('message');

        // Add icon based on type
        let icon = '';
        switch(type) {
            case 'success':
                icon = '<i class="fas fa-check-circle"></i> ';
                break;
            case 'error':
                icon = '<i class="fas fa-exclamation-triangle"></i> ';
                break;
            case 'info':
                icon = '<i class="fas fa-info-circle"></i> ';
                break;
        }

        messageEl.innerHTML = icon + message;
        messageEl.className = `message ${type}`;
        messageEl.classList.remove('hidden');

        // Initial animation state
        messageEl.style.transform = 'translateX(100%) scale(0.8)';
        messageEl.style.opacity = '0';

        // Trigger reflow
        messageEl.offsetHeight;

        // Animate in
        messageEl.style.transition = 'all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
        messageEl.style.transform = 'translateX(0) scale(1)';
        messageEl.style.opacity = '1';

        // Auto hide after 5 seconds with animation
        setTimeout(() => {
            messageEl.style.transition = 'all 0.3s ease-out';
            messageEl.style.transform = 'translateX(100%) scale(0.8)';
            messageEl.style.opacity = '0';

            setTimeout(() => {
                messageEl.classList.add('hidden');
            }, 300);
        }, 5000);
    }

    // Window control functions
    async minimizeWindow() {
        try {
            await window.electronAPI.minimizeWindow();
        } catch (error) {
            console.error('Error minimizing window:', error);
        }
    }

    async closeWindow() {
        try {
            // Show confirmation dialog before closing
            const confirmClose = confirm('நீங்கள் பயன்பாட்டை மூட விரும்புகிறீர்களா?\nDo you want to close the application?');
            if (confirmClose) {
                await window.electronAPI.closeWindow();
            }
        } catch (error) {
            console.error('Error closing window:', error);
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new JadhagamApp();

    // Add keyboard shortcuts
    document.addEventListener('keydown', (event) => {
        // F11 for fullscreen toggle (handled in main process)
        // Alt+F4 for close (handled in main process)
        // Escape for minimize (handled in main process)

        // Ctrl+Q for quit
        if (event.ctrlKey && event.key === 'q') {
            event.preventDefault();
            window.app.closeWindow();
        }

        // Ctrl+M for minimize
        if (event.ctrlKey && event.key === 'm') {
            event.preventDefault();
            window.app.minimizeWindow();
        }
    });
});
