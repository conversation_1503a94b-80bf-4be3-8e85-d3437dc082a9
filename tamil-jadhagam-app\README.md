# Tamil Jadhagam Manager - தமிழ் ஜாதகம் மேலாண்மை

A Windows desktop application for managing Tamil Jadhagam (horoscope) documents with full Tamil language support.

## Features - அம்சங்கள்

### Core Functionality
- **Document Upload** - Upload Jadhagam documents (PDF, Images)
- **Tamil Language Support** - Full Tamil interface with proper font rendering
- **Search Functionality** - Search by <PERSON><PERSON>, <PERSON><PERSON>hir<PERSON>, Jadthi, and City
- **Database Storage** - SQLite database for reliable data storage
- **Document Management** - Organize and access documents easily

### Tamil Categories - தமிழ் வகைகள்
- **<PERSON><PERSON> (ராசி)** - 12 zodiac signs in Tamil
- **Nathathiram (நட்சத்திரம்)** - 27 star constellations
- **Jadthi (ஜாதி)** - Caste/community categories
- **City (நகரம்)** - Tamil Nadu cities and locations

## Installation - நிறுவல்

### Prerequisites
- Windows 10 or later
- Node.js 16+ (for development)

### For End Users
1. Download the installer: `Tamil-Jadhagam-Manager-Setup.exe`
2. Run the installer as administrator
3. Follow the installation wizard
4. Launch from Start Menu or Desktop shortcut

### For Developers
```bash
# Clone the repository
git clone <repository-url>
cd tamil-jadhagam-app

# Install dependencies
npm install

# Run in development mode
npm start

# Build for production
npm run build

# Create installer
npm run dist
```

## Usage - பயன்பாடு

### 1. Upload Jadhagam - ஜாதகம் பதிவேற்றம்
1. Click on "பதிவேற்றம் - Upload" tab
2. Fill in the required information:
   - **Name (பெயர்)** - Person's name
   - **Rasi (ராசி)** - Select zodiac sign
   - **Nathathiram (நட்சத்திரம்)** - Select star constellation
   - **Jadthi (ஜாதி)** - Select community
   - **City (நகரம்)** - Select city
3. Optional information:
   - Birth date and time
   - Father's and Mother's names
   - Gender
   - Notes
4. Click "ஜாதகம் பதிவேற்றம் - Upload Jadhagam"
5. Select the document file (PDF or image)
6. Document will be uploaded and stored

### 2. Search Jadhagam - ஜாதகம் தேடல்
1. Click on "தேடல் - Search" tab
2. Enter search criteria (any combination):
   - Name
   - Rasi
   - Nathathiram
   - Jadthi
   - City
3. Click "தேடல் - Search"
4. Results will be displayed in the "முடிவுகள் - Results" tab

### 3. View Results - முடிவுகள் பார்வை
1. Search results show all matching documents
2. Each result displays:
   - Person's details
   - Upload date
   - All stored information
3. Two action buttons available:
   - **"ஆவணத்தைத் திற - Open Document"**: Opens the file with default application
   - **"பதிவிறக்கம் - Download"**: Downloads a copy to your chosen location

## Database Schema - தரவுத்தள அமைப்பு

### Main Tables
- **jadhagam_documents** - Main document storage
- **rasi_list** - Zodiac signs (Tamil/English)
- **nathathiram_list** - Star constellations (Tamil/English)
- **jadthi_list** - Community categories (Tamil/English)
- **city_list** - Cities and locations (Tamil/English)

### Document Fields
- Personal information (name, gender, birth details)
- Astrological data (rasi, nathathiram)
- Social data (jadthi, city)
- File information (path, size, upload date)
- Additional notes

## Technical Details - தொழில்நுட்ப விவரங்கள்

### Technology Stack
- **Frontend**: HTML5, CSS3, JavaScript
- **Backend**: Electron (Node.js)
- **Database**: SQLite3
- **UI Framework**: Custom CSS with Tamil font support
- **File Handling**: Native file system APIs

### File Storage
- Documents stored in user data directory
- Automatic file organization by timestamp
- Support for PDF and image formats
- File integrity maintained with original names

### Tamil Language Support
- Noto Sans Tamil font for proper rendering
- Unicode Tamil text support
- Bilingual interface (Tamil/English)
- Tamil date and time formatting

## Building Installer - நிறுவல் கோப்பு உருவாக்கம்

### Create Windows Installer
```bash
# Build the application
npm run build

# Create installer package
npm run dist
```

The installer will be created in the `out/make/squirrel.windows/x64/` directory.

### Installer Features
- Windows Squirrel installer
- Automatic updates support
- Start menu integration
- Desktop shortcut creation
- Uninstaller included

## Troubleshooting - சிக்கல் தீர்வு

### Common Issues
1. **Database not loading**: Check file permissions in user data directory
2. **Tamil text not displaying**: Ensure Noto Sans Tamil font is available
3. **File upload fails**: Check disk space and file permissions
4. **Search not working**: Verify database connection

### Support
For technical support or feature requests, please contact the development team.

## License - உரிமம்

MIT License - Free for personal and commercial use.

## Version History - பதிப்பு வரலாறு

### v1.0.0
- Initial release
- Basic upload and search functionality
- Tamil language support
- Windows installer

---

**தமிழ் ஜாதகம் மேலாண்மை - Tamil Jadhagam Manager**
*Preserving Tamil astrological heritage through modern technology*
