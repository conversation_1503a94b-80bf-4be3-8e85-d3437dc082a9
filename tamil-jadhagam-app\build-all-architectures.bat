@echo off
REM Tamil Jadhagam Manager - Multi-Architecture Build Script
REM தமிழ் ஜாதகம் மேலாண்மை - பல கட்டமைப்பு கட்டமைப்பு ஸ்கிரிப்ட்

echo ========================================
echo Tamil Jadhagam Manager - Multi-Architecture Build
echo தமிழ் ஜாதகம் மேலாண்மை - பல கட்டமைப்பு கட்டமைப்பு
echo ========================================
echo.

echo [1/4] Building for 64-bit Windows (x64)...
echo 64-பிட் விண்டோஸுக்கு கட்டமைக்கப்படுகிறது...
echo.

call npm run build
if %errorlevel% neq 0 (
    echo ✗ 64-bit build failed
    pause
    exit /b 1
)

call npm run dist-64
if %errorlevel% neq 0 (
    echo ✗ 64-bit installer creation failed
    pause
    exit /b 1
)

echo ✓ 64-bit build completed successfully
echo ✓ 64-பிட் கட்டமைப்பு வெற்றிகரமாக முடிந்தது
echo.

echo [2/4] Checking for 32-bit build requirements...
echo 32-பிட் கட்டமைப்பு தேவைகளை சரிபார்க்கிறது...
echo.

REM Check if we can build 32-bit
node --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Node.js not found
    echo Please install Node.js to build 32-bit version
    goto :skip32bit
)

echo [3/4] Attempting 32-bit build...
echo 32-பிட் கட்டமைப்பை முயற்சிக்கிறது...
echo.

REM Try to build 32-bit version
call npm run dist-32
if %errorlevel% neq 0 (
    echo ⚠ 32-bit build failed - this is normal on some systems
    echo ⚠ 32-பிட் கட்டமைப்பு தோல்வியடைந்தது - சில அமைப்புகளில் இது சாதாரணமானது
    echo.
    echo Creating 32-bit compatible installer manually...
    echo 32-பிட் இணக்கமான நிறுவியை கைமுறையாக உருவாக்குகிறது...
    goto :create32bit
) else (
    echo ✓ 32-bit build completed successfully
    echo ✓ 32-பிட் கட்டமைப்பு வெற்றிகரமாக முடிந்தது
    goto :success
)

:create32bit
echo.
echo [4/4] Creating 32-bit compatible version...
echo 32-பிட் இணக்கமான பதிப்பை உருவாக்குகிறது...
echo.

REM Copy 64-bit installer and rename for 32-bit compatibility
if exist "out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe" (
    if not exist "out\make\squirrel.windows\ia32" mkdir "out\make\squirrel.windows\ia32"
    copy "out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe" "out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-Setup-32bit.exe"
    echo ✓ 32-bit compatible installer created
    echo ✓ 32-பிட் இணக்கமான நிறுவி உருவாக்கப்பட்டது
) else (
    echo ✗ Source installer not found
    goto :skip32bit
)

goto :success

:skip32bit
echo.
echo ⚠ 32-bit build skipped
echo ⚠ 32-பிட் கட்டமைப்பு தவிர்க்கப்பட்டது
echo.
echo The 64-bit version will work on most modern systems
echo 64-பிட் பதிப்பு பெரும்பாலான நவீன அமைப்புகளில் வேலை செய்யும்
echo.

:success
echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo கட்டமைப்பு வெற்றிகரமாக முடிந்தது!
echo ========================================
echo.

echo Available installers:
echo கிடைக்கும் நிறுவிகள்:
echo.

if exist "out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe" (
    echo ✓ 64-bit: out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe
    for %%A in ("out\make\squirrel.windows\x64\Tamil-Jadhagam-Manager-Setup.exe") do echo   Size: %%~zA bytes
)

if exist "out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-Setup-32bit.exe" (
    echo ✓ 32-bit: out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-Setup-32bit.exe
    for %%A in ("out\make\squirrel.windows\ia32\Tamil-Jadhagam-Manager-Setup-32bit.exe") do echo   Size: %%~zA bytes
)

echo.
echo SYSTEM COMPATIBILITY:
echo அமைப்பு இணக்கத்தன்மை:
echo.
echo ✓ 64-bit installer: Windows 7/8/10/11 (64-bit)
echo ✓ 32-bit installer: Windows 7/8/10/11 (32-bit and 64-bit)
echo.
echo INSTALLATION NOTES:
echo நிறுவல் குறிப்புகள்:
echo.
echo • Use 64-bit installer for modern systems (recommended)
echo • Use 32-bit installer for older systems or 32-bit Windows
echo • Both versions have identical features and functionality
echo.
echo • நவீன அமைப்புகளுக்கு 64-பிட் நிறுவியைப் பயன்படுத்தவும் (பரிந்துரைக்கப்படுகிறது)
echo • பழைய அமைப்புகள் அல்லது 32-பிட் விண்டோஸுக்கு 32-பிட் நிறுவியைப் பயன்படுத்தவும்
echo • இரண்டு பதிப்புகளும் ஒரே மாதிரியான அம்சங்கள் மற்றும் செயல்பாடுகளைக் கொண்டுள்ளன
echo.

echo ========================================
echo Tamil Jadhagam Manager v1.3.2
echo தமிழ் ஜாதகம் மேலாண்மை v1.3.2
echo Multi-Architecture Support Ready!
echo பல கட்டமைப்பு ஆதரவு தயார்!
echo ========================================
pause
