const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('node:path');
const fs = require('fs');
const JadhagamDatabase = require('./database');

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

let mainWindow;
let database;

const createWindow = () => {
  // Create the browser window in fullscreen mode
  mainWindow = new BrowserWindow({
    fullscreen: true,
    frame: false, // Remove window frame for true fullscreen
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
    title: 'தமிழ் ஜாதகம் மேலாண்மை - Tamil Jadhagam Manager',
    icon: path.join(__dirname, 'assets', 'app-icon.png'), // Custom Tamil "ஜ" icon
    autoHideMenuBar: true, // Hide menu bar
    menuBarVisible: false, // Ensure menu bar is not visible
    titleBarStyle: 'hidden', // Hide title bar
    show: false // Don't show until ready
  });

  // Remove the default menu completely
  mainWindow.setMenu(null);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Set fullscreen after showing
    mainWindow.setFullScreen(true);
  });

  // Load the index.html of the app
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Handle fullscreen toggle with F11 key
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'F11' && input.type === 'keyDown') {
      const isFullScreen = mainWindow.isFullScreen();
      mainWindow.setFullScreen(!isFullScreen);
    }

    // Handle Alt+F4 to close application
    if (input.key === 'F4' && input.alt && input.type === 'keyDown') {
      mainWindow.close();
    }

    // Handle Escape key to minimize or close
    if (input.key === 'Escape' && input.type === 'keyDown') {
      if (mainWindow.isFullScreen()) {
        mainWindow.minimize();
      }
    }
  });

  // Open the DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
};

// Initialize database
const initializeDatabase = async () => {
  try {
    database = new JadhagamDatabase();
    await database.initialize();
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
  }
};

// IPC Handlers
ipcMain.handle('get-rasi-list', async () => {
  try {
    return await database.getAllRasi();
  } catch (error) {
    console.error('Error getting rasi list:', error);
    throw error;
  }
});

ipcMain.handle('get-nathathiram-list', async () => {
  try {
    return await database.getAllNathathiram();
  } catch (error) {
    console.error('Error getting nathathiram list:', error);
    throw error;
  }
});

ipcMain.handle('get-jadthi-list', async () => {
  try {
    return await database.getAllJadthi();
  } catch (error) {
    console.error('Error getting jadthi list:', error);
    throw error;
  }
});

ipcMain.handle('get-city-list', async () => {
  try {
    return await database.getAllCities();
  } catch (error) {
    console.error('Error getting city list:', error);
    throw error;
  }
});

ipcMain.handle('upload-jadhagam', async (event, jadhagamData) => {
  try {
    // Show file dialog to select jadhagam document
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'ஜாதகம் ஆவணத்தைத் தேர்ந்தெடுக்கவும் - Select Jadhagam Document',
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'Image Files', extensions: ['jpg', 'jpeg', 'png', 'gif'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    });

    if (result.canceled) {
      return { success: false, message: 'File selection cancelled' };
    }

    const filePath = result.filePaths[0];
    const fileName = path.basename(filePath);
    const fileStats = fs.statSync(filePath);

    // Create documents directory in user data
    const userDataPath = app.getPath('userData');
    const documentsDir = path.join(userDataPath, 'documents');

    if (!fs.existsSync(documentsDir)) {
      fs.mkdirSync(documentsDir, { recursive: true });
    }

    // Copy file to documents directory with safe filename
    const timestamp = Date.now();
    const safeFileName = fileName.replace(/[<>:"/\\|?*]/g, '_'); // Replace invalid characters
    const newFilePath = path.join(documentsDir, `${timestamp}_${safeFileName}`);
    fs.copyFileSync(filePath, newFilePath);

    // Prepare data for database
    const dbData = {
      ...jadhagamData,
      file_path: newFilePath,
      file_name: fileName,
      file_size: fileStats.size
    };

    const id = await database.insertJadhagam(dbData);
    return { success: true, id, message: 'ஜாதகம் வெற்றிகரமாக பதிவேற்றப்பட்டது - Jadhagam uploaded successfully' };
  } catch (error) {
    console.error('Error uploading jadhagam:', error);
    return { success: false, message: 'Upload failed: ' + error.message };
  }
});

ipcMain.handle('search-jadhagam', async (event, searchParams) => {
  try {
    const results = await database.searchJadhagam(searchParams);
    return { success: true, data: results };
  } catch (error) {
    console.error('Error searching jadhagam:', error);
    return { success: false, message: 'Search failed: ' + error.message };
  }
});

ipcMain.handle('open-document', async (event, filePath) => {
  try {
    const { shell } = require('electron');

    // Check if file exists first
    if (!fs.existsSync(filePath)) {
      return { success: false, message: 'File not found: ' + filePath };
    }

    // Use shell.openPath with proper path handling
    const result = await shell.openPath(path.resolve(filePath));

    // If result is empty string, it means success
    // If result contains text, it's an error message
    if (result === '') {
      return { success: true };
    } else {
      return { success: false, message: 'Failed to open document: ' + result };
    }
  } catch (error) {
    console.error('Error opening document:', error);
    return { success: false, message: 'Failed to open document: ' + error.message };
  }
});

ipcMain.handle('download-document', async (event, filePath, originalFileName) => {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return { success: false, message: 'File not found: ' + filePath };
    }

    // Show save dialog
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'ஆவணத்தை சேமிக்கவும் - Save Document',
      defaultPath: originalFileName,
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'Image Files', extensions: ['jpg', 'jpeg', 'png', 'gif'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });

    if (result.canceled) {
      return { success: false, message: 'Download cancelled' };
    }

    // Copy file to selected location
    fs.copyFileSync(filePath, result.filePath);

    return {
      success: true,
      message: 'ஆவணம் வெற்றிகரமாக பதிவிறக்கப்பட்டது - Document downloaded successfully',
      downloadPath: result.filePath
    };
  } catch (error) {
    console.error('Error downloading document:', error);
    return { success: false, message: 'Download failed: ' + error.message };
  }
});

ipcMain.handle('delete-document', async (event, documentId, filePath) => {
  try {
    // Show confirmation dialog
    const result = await dialog.showMessageBox(mainWindow, {
      type: 'warning',
      title: 'ஆவணத்தை நீக்கவும் - Delete Document',
      message: 'இந்த ஆவணத்தை நிரந்தரமாக நீக்க விரும்புகிறீர்களா?\nDo you want to permanently delete this document?',
      detail: 'இந்த செயல்பாட்டை மாற்ற முடியாது.\nThis action cannot be undone.',
      buttons: [
        'நீக்கு - Delete',
        'ரத்து செய் - Cancel'
      ],
      defaultId: 1, // Cancel is default
      cancelId: 1
    });

    if (result.response === 1) {
      return { success: false, message: 'Delete cancelled' };
    }

    // Delete from database first
    await new Promise((resolve, reject) => {
      database.db.run("DELETE FROM jadhagam_documents WHERE id = ?", [documentId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });

    // Delete file from filesystem if it exists
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    return {
      success: true,
      message: 'ஆவணம் வெற்றிகரமாக நீக்கப்பட்டது - Document deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting document:', error);
    return { success: false, message: 'Delete failed: ' + error.message };
  }
});

// Window control handlers
ipcMain.handle('minimize-window', async () => {
  try {
    if (mainWindow) {
      mainWindow.minimize();
    }
    return { success: true };
  } catch (error) {
    console.error('Error minimizing window:', error);
    return { success: false, message: 'Failed to minimize window' };
  }
});

ipcMain.handle('close-window', async () => {
  try {
    if (mainWindow) {
      mainWindow.close();
    }
    return { success: true };
  } catch (error) {
    console.error('Error closing window:', error);
    return { success: false, message: 'Failed to close window' };
  }
});

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
app.whenReady().then(async () => {
  await initializeDatabase();
  createWindow();

  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed, except on macOS.
app.on('window-all-closed', () => {
  if (database) {
    database.close();
  }
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
