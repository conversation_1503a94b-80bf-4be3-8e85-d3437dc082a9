const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { app } = require('electron');

class JadhagamDatabase {
    constructor() {
        // Create database in user data directory
        const userDataPath = app.getPath('userData');
        this.dbPath = path.join(userDataPath, 'jadhagam.db');
        this.db = null;
    }

    async initialize() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('Error opening database:', err);
                    reject(err);
                } else {
                    console.log('Connected to SQLite database');
                    this.createTables().then(resolve).catch(reject);
                }
            });
        });
    }

    async createTables() {
        return new Promise((resolve, reject) => {
            const createJadhagamTable = `
                CREATE TABLE IF NOT EXISTS jadhagam_documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    rasi TEXT NOT NULL,
                    nathathiram TEXT NOT NULL,
                    jadthi TEXT NOT NULL,
                    city TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_name TEXT NOT NULL,
                    file_size INTEGER,
                    upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT,
                    birth_date DATE,
                    birth_time TIME,
                    gender TEXT,
                    father_name TEXT,
                    mother_name TEXT
                );
            `;

            const createRasiTable = `
                CREATE TABLE IF NOT EXISTS rasi_list (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rasi_tamil TEXT NOT NULL UNIQUE,
                    rasi_english TEXT NOT NULL
                );
            `;

            const createNathathiramTable = `
                CREATE TABLE IF NOT EXISTS nathathiram_list (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nathathiram_tamil TEXT NOT NULL UNIQUE,
                    nathathiram_english TEXT NOT NULL,
                    rasi_id INTEGER,
                    FOREIGN KEY (rasi_id) REFERENCES rasi_list (id)
                );
            `;

            const createJadthiTable = `
                CREATE TABLE IF NOT EXISTS jadthi_list (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    jadthi_tamil TEXT NOT NULL UNIQUE,
                    jadthi_english TEXT NOT NULL
                );
            `;

            const createCityTable = `
                CREATE TABLE IF NOT EXISTS city_list (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    city_tamil TEXT NOT NULL,
                    city_english TEXT NOT NULL,
                    state_tamil TEXT,
                    state_english TEXT,
                    country TEXT DEFAULT 'India'
                );
            `;

            // Execute all table creation queries
            this.db.serialize(() => {
                this.db.run(createJadhagamTable);
                this.db.run(createRasiTable);
                this.db.run(createNathathiramTable);
                this.db.run(createJadthiTable);
                this.db.run(createCityTable, (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        this.insertDefaultData().then(resolve).catch(reject);
                    }
                });
            });
        });
    }

    async insertDefaultData() {
        return new Promise((resolve, reject) => {
            // Insert default Rasi data
            const rasiData = [
                ['மேஷம்', 'Mesham'],
                ['ரிஷபம்', 'Rishabam'],
                ['மிதுனம்', 'Mithunam'],
                ['கடகம்', 'Kadagam'],
                ['சிம்மம்', 'Simmam'],
                ['கன்னி', 'Kanni'],
                ['துலாம்', 'Thulam'],
                ['விருச்சிகம்', 'Viruchigam'],
                ['தனுசு', 'Dhanusu'],
                ['மகரம்', 'Magaram'],
                ['கும்பம்', 'Kumbam'],
                ['மீனம்', 'Meenam']
            ];

            // Insert default Nathathiram data
            const nathathiramData = [
                ['அசுவினி', 'Aswini'],
                ['பரணி', 'Bharani'],
                ['கிருத்திகை', 'Krittika'],
                ['ரோகிணி', 'Rohini'],
                ['மிருகசீரிடம்', 'Mrigashirsha'],
                ['திருவாதிரை', 'Ardra'],
                ['புனர்பூசம்', 'Punarvasu'],
                ['பூசம்', 'Pushya'],
                ['ஆயில்யம்', 'Ashlesha'],
                ['மகம்', 'Magha'],
                ['பூரம்', 'Purva Phalguni'],
                ['உத்திரம்', 'Uttara Phalguni'],
                ['ஹஸ்தம்', 'Hasta'],
                ['சித்திரை', 'Chitra'],
                ['சுவாதி', 'Swati'],
                ['விசாகம்', 'Vishakha'],
                ['அனுஷம்', 'Anuradha'],
                ['கேட்டை', 'Jyeshtha'],
                ['மூலம்', 'Mula'],
                ['பூராடம்', 'Purva Ashadha'],
                ['உத்திராடம்', 'Uttara Ashadha'],
                ['திருவோணம்', 'Shravana'],
                ['அவிட்டம்', 'Dhanishta'],
                ['சதயம்', 'Shatabhisha'],
                ['பூரட்டாதி', 'Purva Bhadrapada'],
                ['உத்திரட்டாதி', 'Uttara Bhadrapada'],
                ['ரேவதி', 'Revati']
            ];

            // Insert default Jadthi data
            const jadthiData = [
                ['பிராமணர்', 'Brahmin'],
                ['க்ஷத்திரியர்', 'Kshatriya'],
                ['வைசியர்', 'Vaishya'],
                ['சூத்திரர்', 'Shudra'],
                ['அரசர்', 'Royal'],
                ['வணிகர்', 'Merchant'],
                ['விவசாயி', 'Farmer'],
                ['கைவினைஞர்', 'Artisan']
            ];

            // Insert default city data
            const cityData = [
                ['சென்னை', 'Chennai', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['கோயம்புத்தூர்', 'Coimbatore', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['மதுரை', 'Madurai', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['திருச்சி', 'Trichy', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['சேலம்', 'Salem', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['திருநெல்வேலி', 'Tirunelveli', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['ஈரோடு', 'Erode', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['வேலூர்', 'Vellore', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['தூத்துக்குடி', 'Thoothukudi', 'தமிழ்நாடு', 'Tamil Nadu'],
                ['திருவண்ணாமலை', 'Tiruvannamalai', 'தமிழ்நாடு', 'Tamil Nadu']
            ];

            this.db.serialize(() => {
                // Check if data already exists
                this.db.get("SELECT COUNT(*) as count FROM rasi_list", (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    if (row.count === 0) {
                        // Insert Rasi data
                        const rasiStmt = this.db.prepare("INSERT INTO rasi_list (rasi_tamil, rasi_english) VALUES (?, ?)");
                        rasiData.forEach(rasi => rasiStmt.run(rasi));
                        rasiStmt.finalize();

                        // Insert Nathathiram data
                        const nathathiramStmt = this.db.prepare("INSERT INTO nathathiram_list (nathathiram_tamil, nathathiram_english) VALUES (?, ?)");
                        nathathiramData.forEach(nathathiram => nathathiramStmt.run(nathathiram));
                        nathathiramStmt.finalize();

                        // Insert Jadthi data
                        const jadthiStmt = this.db.prepare("INSERT INTO jadthi_list (jadthi_tamil, jadthi_english) VALUES (?, ?)");
                        jadthiData.forEach(jadthi => jadthiStmt.run(jadthi));
                        jadthiStmt.finalize();

                        // Insert City data
                        const cityStmt = this.db.prepare("INSERT INTO city_list (city_tamil, city_english, state_tamil, state_english) VALUES (?, ?, ?, ?)");
                        cityData.forEach(city => cityStmt.run(city));
                        cityStmt.finalize(() => {
                            resolve();
                        });
                    } else {
                        resolve();
                    }
                });
            });
        });
    }

    // CRUD operations for Jadhagam documents
    async insertJadhagam(data) {
        return new Promise((resolve, reject) => {
            const stmt = this.db.prepare(`
                INSERT INTO jadhagam_documents 
                (name, rasi, nathathiram, jadthi, city, file_path, file_name, file_size, birth_date, birth_time, gender, father_name, mother_name, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);
            
            stmt.run([
                data.name, data.rasi, data.nathathiram, data.jadthi, data.city,
                data.file_path, data.file_name, data.file_size, data.birth_date,
                data.birth_time, data.gender, data.father_name, data.mother_name, data.notes
            ], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve(this.lastID);
                }
            });
            stmt.finalize();
        });
    }

    async searchJadhagam(searchParams) {
        return new Promise((resolve, reject) => {
            let query = "SELECT * FROM jadhagam_documents WHERE 1=1";
            let params = [];

            if (searchParams.rasi) {
                query += " AND rasi LIKE ?";
                params.push(`%${searchParams.rasi}%`);
            }
            if (searchParams.nathathiram) {
                query += " AND nathathiram LIKE ?";
                params.push(`%${searchParams.nathathiram}%`);
            }
            if (searchParams.jadthi) {
                query += " AND jadthi LIKE ?";
                params.push(`%${searchParams.jadthi}%`);
            }
            if (searchParams.city) {
                query += " AND city LIKE ?";
                params.push(`%${searchParams.city}%`);
            }
            if (searchParams.name) {
                query += " AND name LIKE ?";
                params.push(`%${searchParams.name}%`);
            }

            query += " ORDER BY upload_date DESC";

            this.db.all(query, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async getAllRasi() {
        return new Promise((resolve, reject) => {
            this.db.all("SELECT * FROM rasi_list ORDER BY id", (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    async getAllNathathiram() {
        return new Promise((resolve, reject) => {
            this.db.all("SELECT * FROM nathathiram_list ORDER BY id", (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    async getAllJadthi() {
        return new Promise((resolve, reject) => {
            this.db.all("SELECT * FROM jadthi_list ORDER BY id", (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    async getAllCities() {
        return new Promise((resolve, reject) => {
            this.db.all("SELECT * FROM city_list ORDER BY city_tamil", (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }

    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err);
                } else {
                    console.log('Database connection closed');
                }
            });
        }
    }
}

module.exports = JadhagamDatabase;
